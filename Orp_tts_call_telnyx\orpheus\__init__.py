# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from .models import TTSEncoding, OrpheusTTSModels, OrpheusVoices
from .tts import DEFAULT_VOICE, OrpheusTTS, Voice, VoiceSettings, DEFAULT_API_URL, DEFAULT_API_KEY

__version__ = "0.1.0"

__all__ = [
    "OrpheusTTS",
    "Voice",
    "VoiceSettings",
    "TTSEncoding",
    "OrpheusTTSModels",
    "OrpheusVoices",
    "DEFAULT_VOICE",
    "DEFAULT_API_URL",
    "DEFAULT_API_KEY",
    "__version__",
]

from livekit.agents import Plugin
from .log import logger


class OrpheusPlugin(Plugin):
    def __init__(self):
        super().__init__(__name__, __version__, __package__, logger)


# Register the plugin
Plugin.register_plugin(OrpheusPlugin())

# Cleanup docs of unexported modules
_module = dir()
NOT_IN_ALL = [m for m in _module if m not in __all__]

__pdoc__ = {}

for n in NOT_IN_ALL:
    __pdoc__[n] = False
