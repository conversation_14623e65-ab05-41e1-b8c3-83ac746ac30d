import os
import json
import time
import logging
import subprocess
import threading
import uuid
import requests
import io
import sys
import socket
import multiprocessing
from flask import Flask, request, jsonify, send_file, current_app
from flask_cors import CORS
from datetime import datetime, timezone
from dataclasses import dataclass, field
from typing import Dict, List, Optional
import base64
import tempfile
from werkzeug.utils import secure_filename
import re
from groq import Groq  # Still needed for LLM models endpoint
import telnyx
from tts_service import test_tts, generate_tts, get_available_voices
import time as time_module
from threading import Timer
from pathlib import Path

# Import campaign utilities
try:
    from campaign_statistics import CampaignStats, get_campaign_statistics, get_unresponsive_contacts
    from recycle_unresponsive import RecycleUnresponsive, create_recycled_campaign, recycle_campaign_in_place
    from call_log_analyzer import CallLogAnalyzer
    from telnyx_call_analyzer import TelnyxCallAnalyzer
    CAMPAIGN_STATS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Campaign statistics modules not available: {e}")
    CAMPAIGN_STATS_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default System Prompt
DEFAULT_SYSTEM_PROMPT = """# You are a helpful and friendly AI assistant.

You are designed to be helpful, harmless, and honest in all your interactions. Your primary goal is to assist the caller with any questions or tasks they might have.

## Guidelines for your conversation:
- Be friendly and conversational, but professional.
- Provide comprehensive and accurate information.
- Ask clarifying questions when needed.
- Avoid making assumptions about the caller.
- Be respectful of the caller's time.
- Offer additional help when appropriate.

You have a wide range of knowledge and can help with many topics including technology, science, history, culture, and more.
"""

# Default Emotional System Prompt
DEFAULT_EMOTIONAL_PROMPT = """You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:

<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.
<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.
<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.
Guidelines for Using Emotional Tags
Natural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.
Show, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of "I'm happy," use <giggle> in a cheerful response).
Contextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.
Sparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.
Professional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).
Objective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.
Example Usage
Greeting: "Great to connect, <chuckle>! Got a moment to chat?"</chuckle>
Clarification: "Thanks for sharing, <chuckle>! Could you tell me a bit more?"</chuckle>
Handling Hesitation: "I totally get it, <giggle>. What's most important to you right now?"</giggle>"""

AGENT_CONFIG = {
    "1": {
        "name": "Mia",
        "voice_id": "elise",  # Example voice ID, adjust as needed
        "model_id": "llama-3.3-70b-versatile", # Example model, adjust as needed
        "system_prompt": DEFAULT_SYSTEM_PROMPT
    },
    "2": {
        "name": "David",
        "voice_id": "david", # Example voice ID
        "model_id": "gpt-4o", # Example model
        "system_prompt": DEFAULT_SYSTEM_PROMPT.replace("helpful and friendly AI assistant", "knowledgeable and efficient AI support agent")
    }
    # Add more agents here as needed
}

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Database storage using SQLite (replacing JSON files for better reliability)
import json
import sqlite3
import threading
from contextlib import contextmanager
from pathlib import Path

# Create data directory
DATA_DIR = Path(__file__).parent / "data"
DATA_DIR.mkdir(exist_ok=True)

# Database path (replaces individual JSON files)
DATABASE_PATH = DATA_DIR / "quickcall.db"

class QuickCallDB:
    """SQLite database manager for QuickCall backend"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.local = threading.local()
        self.initialize_database()
    
    @contextmanager
    def get_connection(self):
        """Get a thread-local database connection"""
        if not hasattr(self.local, 'connection'):
            self.local.connection = sqlite3.connect(
                self.db_path, check_same_thread=False  # REMOVED 30-second timeout
            )
            self.local.connection.row_factory = sqlite3.Row
            # Enable WAL mode for better concurrent access
            self.local.connection.execute("PRAGMA journal_mode=WAL")
            self.local.connection.execute("PRAGMA synchronous=NORMAL")
        
        try:
            yield self.local.connection
        except Exception as e:
            self.local.connection.rollback()
            raise e
    
    def initialize_database(self):
        """Create database tables if they don't exist"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Campaigns table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS campaigns (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    agent_id TEXT,
                    status TEXT DEFAULT 'draft',
                    telnyx_numbers TEXT,
                    contacts TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    pause_requested BOOLEAN DEFAULT 0,
                    paused_at TIMESTAMP,
                    resumed_at TIMESTAMP
                )
            """)
            
            # Call logs table  
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS call_logs (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT,
                    campaign_id TEXT,
                    contact_name TEXT,
                    contact_phone TEXT,
                    from_phone TEXT,
                    agent_name TEXT,
                    status TEXT DEFAULT 'initiated',
                    notes TEXT,
                    call_duration INTEGER DEFAULT 0,
                    recording_url TEXT,
                    telnyx_call_id TEXT,
                    answered_at TEXT,
                    ended_at TEXT,
                    bridged_at TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (campaign_id) REFERENCES campaigns (id)
                )
            """)
            
            # Agents table (for backward compatibility)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS agents (
                    id TEXT PRIMARY KEY,
                    data TEXT
                )
            """)
            
            # SIP trunks table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sip_trunks (
                    phone_number TEXT PRIMARY KEY,
                    trunk_id TEXT NOT NULL
                )
            """)
            
            # Create indexes
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_call_logs_campaign_id ON call_logs(campaign_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_call_logs_status ON call_logs(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_call_logs_contact_phone ON call_logs(contact_phone)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_call_logs_telnyx_call_id ON call_logs(telnyx_call_id)")
            
            conn.commit()
            logger.info("✅ Database initialized successfully")

# Initialize database
qc_db = QuickCallDB(str(DATABASE_PATH))

# Legacy file paths (for backward compatibility)
AGENTS_FILE = DATA_DIR / "agents.json"
CAMPAIGNS_FILE = DATA_DIR / "campaigns.json" 
SIP_TRUNKS_FILE = DATA_DIR / "sip_trunks.json"
CALL_LOGS_FILE = DATA_DIR / "call_logs.json"

def load_json_data(file_path, default=None):
    """Load data from JSON file with corruption recovery (fallback for legacy support)"""
    if default is None:
        default = {}
    
    # If it's the campaigns file, load from database instead
    if str(file_path).endswith("campaigns.json"):
        return load_campaigns_from_db()
    
    # If it's the call logs file, load from database instead  
    if str(file_path).endswith("call_logs.json"):
        return load_call_logs_from_db()
    
    # If it's the agents file, load from database instead
    if str(file_path).endswith("agents.json"):
        return load_agents_from_db()
    
    # If it's the SIP trunks file, load from database instead
    if str(file_path).endswith("sip_trunks.json"):
        return load_sip_trunks_from_db()
    
    # Fallback to original JSON loading for other files
    try:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if not content.strip():
                    logger.warning(f"Empty file: {file_path}, using default")
                    return default
                return json.loads(content)
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error in {file_path}: {e}")
        return default
    except Exception as e:
        logger.error(f"Error loading {file_path}: {e}")
    return default

def save_json_data(file_path, data):
    """Save data to database instead of JSON file (with legacy fallback)"""
    
    # If it's the campaigns file, save to database instead
    if str(file_path).endswith("campaigns.json"):
        return save_campaigns_to_db(data)
    
    # If it's the call logs file, save to database instead
    if str(file_path).endswith("call_logs.json"):
        return save_call_logs_to_db(data)
    
    # If it's the agents file, save to database instead
    if str(file_path).endswith("agents.json"):
        return save_agents_to_db(data)
    
    # If it's the SIP trunks file, save to database instead
    if str(file_path).endswith("sip_trunks.json"):
        return save_sip_trunks_to_db(data)
    
    # For other files, just log (we're not using complex JSON saving anymore)
    logger.info(f"📊 Database operation completed for {file_path}")
    return True

# Database helper functions
def load_campaigns_from_db():
    """Load campaigns from database"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM campaigns ORDER BY created_at DESC")
            campaigns = {}
            for row in cursor.fetchall():
                campaign = dict(row)
                # Parse JSON fields and fix telnyxNumber vs telnyxNumbers issue
                if campaign['telnyx_numbers']:
                    try:
                        campaign['telnyxNumbers'] = json.loads(campaign['telnyx_numbers'])
                    except json.JSONDecodeError:
                        campaign['telnyxNumbers'] = []
                else:
                    campaign['telnyxNumbers'] = []
                
                if campaign['contacts']:
                    try:
                        campaign['contacts'] = json.loads(campaign['contacts'])
                    except json.JSONDecodeError:
                        campaign['contacts'] = []
                else:
                    campaign['contacts'] = []
                
                campaigns[campaign['id']] = campaign
            return campaigns
    except Exception as e:
        logger.error(f"❌ Error loading campaigns from database: {e}")
        return {}

def save_campaigns_to_db(campaigns_data):
    """Save campaigns to database"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            for campaign_id, campaign in campaigns_data.items():
                campaign['id'] = campaign_id
                campaign['updated_at'] = datetime.now().isoformat()
                
                # Prepare JSON fields
                telnyx_numbers_json = json.dumps(campaign.get('telnyxNumbers', []))
                contacts_json = json.dumps(campaign.get('contacts', []))
                
                cursor.execute("SELECT id FROM campaigns WHERE id = ?", (campaign_id,))
                exists = cursor.fetchone() is not None
                
                if exists:
                    cursor.execute("""
                        UPDATE campaigns SET 
                            name = ?, description = ?, agent_id = ?, status = ?,
                            telnyx_numbers = ?, contacts = ?, updated_at = ?,
                            started_at = ?, completed_at = ?, pause_requested = ?,
                            paused_at = ?, resumed_at = ?
                        WHERE id = ?
                    """, (
                        campaign['name'], campaign.get('description', ''),
                        campaign.get('agent_id', ''), campaign.get('status', 'draft'),
                        telnyx_numbers_json, contacts_json, campaign['updated_at'],
                        campaign.get('started_at'), campaign.get('completed_at'),
                        campaign.get('pause_requested', False),
                        campaign.get('paused_at'), campaign.get('resumed_at'),
                        campaign_id
                    ))
                else:
                    cursor.execute("""
                        INSERT INTO campaigns 
                        (id, name, description, agent_id, status, telnyx_numbers, contacts,
                         created_at, updated_at, started_at, completed_at, pause_requested,
                         paused_at, resumed_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        campaign_id, campaign['name'], campaign.get('description', ''),
                        campaign.get('agent_id', ''), campaign.get('status', 'draft'),
                        telnyx_numbers_json, contacts_json, campaign['updated_at'],
                        campaign['updated_at'], campaign.get('started_at'),
                        campaign.get('completed_at'), campaign.get('pause_requested', False),
                        campaign.get('paused_at'), campaign.get('resumed_at')
                    ))
            
            conn.commit()
            logger.info(f"✅ Saved {len(campaigns_data)} campaigns to database")
        return True
    except Exception as e:
        logger.error(f"❌ Error saving campaigns to database: {e}")
        return False

def load_call_logs_from_db():
    """Load call logs from database"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM call_logs ORDER BY created_at DESC")
            return [dict(row) for row in cursor.fetchall()]
    except Exception as e:
        logger.error(f"❌ Error loading call logs from database: {e}")
        return []

def get_all_campaigns():
    """Get all campaigns from database (wrapper function)"""
    return load_campaigns_from_db()

def get_all_call_logs():
    """Get all call logs from database (wrapper function)"""
    return load_call_logs_from_db()

def save_call_logs_to_db(call_logs_data):
    """Save call logs to database"""
    # This is now handled by the log_call function, so just return True
    logger.info(f"📊 Call logs are automatically saved to database")
    return True

def load_agents_from_db():
    """Load agents from database"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM agents")
            agents = {}
            for row in cursor.fetchall():
                try:
                    agents[row['id']] = json.loads(row['data'])
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse agent data for {row['id']}")
            return agents
    except Exception as e:
        logger.error(f"❌ Error loading agents from database: {e}")
        return {}

def save_agents_to_db(agents_data):
    """Save agents to database"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            for agent_id, agent_data in agents_data.items():
                cursor.execute("""
                    INSERT OR REPLACE INTO agents (id, data) VALUES (?, ?)
                """, (agent_id, json.dumps(agent_data)))
            conn.commit()
            logger.info(f"✅ Saved {len(agents_data)} agents to database")
            return True
    except Exception as e:
        logger.error(f"❌ Error saving agents to database: {e}")
        return False

def delete_agent_from_db(agent_id):
    """Delete a specific agent from database"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM agents WHERE id = ?", (agent_id,))
            conn.commit()
            rows_affected = cursor.rowcount
            logger.info(f"✅ Deleted agent {agent_id} from database (rows affected: {rows_affected})")
            return rows_affected > 0
    except Exception as e:
        logger.error(f"❌ Error deleting agent {agent_id} from database: {e}")
        return False

def load_sip_trunks_from_db():
    """Load SIP trunks from database"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT phone_number, trunk_id FROM sip_trunks")
            return {row[0]: row[1] for row in cursor.fetchall()}
    except Exception as e:
        logger.error(f"❌ Error loading SIP trunks from database: {e}")
        return {}

def save_sip_trunks_to_db(sip_trunks_data):
    """Save SIP trunks to database"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            for phone_number, trunk_id in sip_trunks_data.items():
                cursor.execute("""
                    INSERT OR REPLACE INTO sip_trunks (phone_number, trunk_id) VALUES (?, ?)
                """, (phone_number, trunk_id))
            conn.commit()
            logger.info(f"✅ Saved {len(sip_trunks_data)} SIP trunk mappings to database")
            return True
    except Exception as e:
        logger.error(f"❌ Error saving SIP trunks to database: {e}")
        return False

def log_call(call_data):
    """Log a call to database storage"""
    call_log_entry = {
        'id': str(uuid.uuid4()),
        'timestamp': datetime.now().isoformat(),
        'contact_name': call_data.get('contact_name', 'Unknown'),
        'contact_phone': call_data.get('contact_phone', ''),
        'from_phone': call_data.get('from_phone', ''),
        'agent_name': call_data.get('agent_name', ''),
        'campaign_id': call_data.get('campaign_id', ''),
        'status': call_data.get('status', 'initiated'),
        'notes': call_data.get('contact_notes', ''),
        'call_duration': call_data.get('call_duration', 0),
        'recording_url': call_data.get('recording_url', ''),
        'telnyx_call_id': call_data.get('telnyx_call_id', '')
    }
    
    # Save directly to database
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO call_logs 
                (id, timestamp, campaign_id, contact_name, contact_phone, from_phone, 
                 agent_name, status, notes, call_duration, recording_url, telnyx_call_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                call_log_entry['id'], call_log_entry['timestamp'], call_log_entry['campaign_id'],
                call_log_entry['contact_name'], call_log_entry['contact_phone'], call_log_entry['from_phone'],
                call_log_entry['agent_name'], call_log_entry['status'], call_log_entry['notes'],
                call_log_entry['call_duration'], call_log_entry['recording_url'], call_log_entry['telnyx_call_id']
            ))
            conn.commit()
        
        logger.info(f"📋 Call logged to database: {call_log_entry['contact_name']} ({call_log_entry['contact_phone']})")
    except Exception as e:
        logger.error(f"❌ Error logging call to database: {e}")
    
    return call_log_entry

# Load persistent data (now from database)
agents_storage = load_agents_from_db()  # Load from database instead of JSON

# Fix ALL agent ID consistency (dynamic migration)
agents_to_fix = {}
for agent_id, agent_data in list(agents_storage.items()):
    # If agent ID is a UUID but agent has a name, use name as ID
    if len(agent_id) > 10 and '-' in agent_id and agent_data.get('name'):
        agent_name = agent_data['name'].lower().replace(' ', '_')
        if agent_name != agent_id:
            logger.info(f"🔧 Fixing agent ID consistency: {agent_id} → {agent_name}")
            agent_data['id'] = agent_name  # Update the ID in the data
            agents_to_fix[agent_name] = agent_data
            agents_to_fix[agent_id] = None  # Mark for deletion

if agents_to_fix:
    # Apply the changes
    for new_id, data in agents_to_fix.items():
        if data is None:
            # Delete the old UUID entry
            if new_id in agents_storage:
                del agents_storage[new_id]
        else:
            # Add with new name-based ID
            agents_storage[new_id] = data
    
    save_agents_to_db(agents_storage)
    logger.info(f"✅ Fixed {len([k for k, v in agents_to_fix.items() if v is not None])} agent IDs for consistency")

# 🔧 FIX: Only migrate JSON agents on first run, not every time (prevents restoring deleted agents)
json_agents = load_json_data(AGENTS_FILE, {})
# Only migrate if database is completely empty (first time setup)
if json_agents and len(agents_storage) == 0:
    logger.info(f"Initial migration: Moving {len(json_agents)} agents from JSON to database")
    for agent_id, agent_data in json_agents.items():
        agents_storage[agent_id] = agent_data
    save_agents_to_db(agents_storage)
    logger.info("✅ Initial agent migration completed")
elif json_agents and len(json_agents) != len(agents_storage):
    logger.info(f"Database ({len(agents_storage)}) and JSON ({len(json_agents)}) agent counts differ - using database as source of truth")

# 🔧 FIX: Load campaigns from database instead of JSON files
campaigns_storage = {}
try:
    # Load campaigns from database first
    db_campaigns = load_campaigns_from_db()
    if db_campaigns:
        campaigns_storage = db_campaigns
        logger.info(f"✅ Loaded {len(campaigns_storage)} campaigns from database")
    else:
        # Fallback to JSON file if database is empty
        json_campaigns = load_json_data(CAMPAIGNS_FILE, {})
        if json_campaigns:
            logger.info(f"📦 Migrating {len(json_campaigns)} campaigns from JSON to database")
            # Ensure phone numbers are properly formatted as arrays
            for campaign_id, campaign in json_campaigns.items():
                # Fix phone number format
                if 'telnyx_numbers' in campaign:
                    if isinstance(campaign['telnyx_numbers'], str):
                        try:
                            campaign['telnyx_numbers'] = json.loads(campaign['telnyx_numbers'])
                        except json.JSONDecodeError:
                            campaign['telnyx_numbers'] = [campaign['telnyx_numbers']] if campaign['telnyx_numbers'] else []
                    elif not isinstance(campaign['telnyx_numbers'], list):
                        campaign['telnyx_numbers'] = []
                
                # Ensure telnyxNumbers field exists for frontend compatibility
                campaign['telnyxNumbers'] = campaign.get('telnyx_numbers', [])
                
            campaigns_storage = json_campaigns
            save_campaigns_to_db(campaigns_storage)
            logger.info("✅ Campaign migration to database completed")
        else:
            campaigns_storage = {}
            logger.info("📝 No existing campaigns found")
except Exception as e:
    logger.error(f"❌ Error loading campaigns: {e}")
    campaigns_storage = {}

sip_trunk_mapping = load_json_data(SIP_TRUNKS_FILE, {})
call_logs = load_json_data(CALL_LOGS_FILE, [])

# Database helper functions for webhook processing
def update_call_status_by_telnyx_id(telnyx_call_id, status, **kwargs):
    """Update call status by Telnyx call ID"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            update_fields = ['status = ?']
            values = [status]
            
            # Add timestamp update manually (since DEFAULT wasn't added)
            update_fields.append('timestamp = ?')
            values.append(datetime.now().isoformat())
            
            # Add optional fields
            for field, value in kwargs.items():
                if field in ['call_duration', 'recording_url', 'answered_at', 'ended_at']:
                    update_fields.append(f'{field} = ?')
                    values.append(value)
            
            values.append(telnyx_call_id)
            
            cursor.execute(f"""
                UPDATE call_logs SET {', '.join(update_fields)}
                WHERE telnyx_call_id = ?
            """, values)
            conn.commit()
            
            if cursor.rowcount > 0:
                logger.info(f"✅ Updated call status for Telnyx ID {telnyx_call_id}: {status}")
                return True
            else:
                logger.warning(f"⚠️ No call found with Telnyx ID: {telnyx_call_id}")
                return False
    except Exception as e:
        logger.error(f"❌ Error updating call status by Telnyx ID: {e}")
        return False

def get_call_by_telnyx_id(telnyx_call_id):
    """Get call by Telnyx call ID"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM call_logs WHERE telnyx_call_id = ?", (telnyx_call_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
    except Exception as e:
        logger.error(f"❌ Error getting call by Telnyx ID: {e}")
        return None

def get_retry_candidates(campaign_id):
    """Get contacts that need retry for a campaign"""
    try:
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            # Get the most recent call for each phone number
            cursor.execute("""
                SELECT cl1.contact_phone, cl1.contact_name, cl1.status, cl1.created_at, cl1.notes
                FROM call_logs cl1
                INNER JOIN (
                    SELECT contact_phone, MAX(created_at) as max_created
                    FROM call_logs
                    WHERE campaign_id = ?
                    GROUP BY contact_phone
                ) cl2 ON cl1.contact_phone = cl2.contact_phone AND cl1.created_at = cl2.max_created
                WHERE cl1.campaign_id = ? AND cl1.status IN ('failed', 'no_answer', 'busy')
                ORDER BY cl1.contact_phone
            """, (campaign_id, campaign_id))
            
            retry_candidates = [dict(row) for row in cursor.fetchall()]
            logger.info(f"📊 Found {len(retry_candidates)} contacts needing retry for campaign {campaign_id}")
            return retry_candidates
    except Exception as e:
        logger.error(f"❌ Error getting retry candidates: {e}")
        return []

# Telnyx configuration
TELNYX_API_KEY = os.getenv('TELNYX_API_KEY', '**********************************************************')
TELNYX_APP_ID = os.getenv('TELNYX_APP_ID', '2702376459367876227')

# Initialize Telnyx
telnyx.api_key = TELNYX_API_KEY

# Default SIP trunk ID (your current one)
DEFAULT_SIP_TRUNK_ID = os.getenv('SIP_OUTBOUND_TRUNK_ID', 'ST_mdGsY9Kf6vPJ')

# LiveKit Configuration
LIVEKIT_URL = os.getenv('LIVEKIT_URL', 'wss://testing-zjqnplxr.livekit.cloud')
LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY', 'APIqFuS9DNsSaxd')
LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET', 'lx5Nthx9u9okwcDfzxjH07VSSSqZ5XNT91pqF83Se0L')

def initialize_default_agents():
    """Initialize default agents in storage if empty"""
    if not agents_storage:
        default_agents = [
            {
                'id': 'mia',
                'name': 'Mia',
                'description': 'Friendly sales assistant',
                'system_prompt': 'You are Mia, a friendly and professional sales assistant.',
                'emotional_prompt': DEFAULT_EMOTIONAL_PROMPT,
                'model_id': 'llama-3.3-70b-versatile',
                'voice_id': 'tara',  # FIXED: Use lowercase for consistency
                'stt_provider': 'groq',  # NEW: Add STT provider
                'chunk_size_bytes': 4096,  # NEW: Add chunk size configuration
                'streaming_mode': 'auto',  # NEW: Add streaming mode preference
                'interrupt_speech_duration': 3.0,
                'allow_interruptions': True,
                'interrupt_min_words': 0,  # NEW: Add missing field
                'preemptive_synthesis': True,  # NEW: Add missing field
                'initial_greeting': 'Hello my name is Mia, how can I help you today?',
                'use_initial_greeting': True,
                'is_active': False
            },
            {
                'id': 'alex',
                'name': 'Alex',
                'description': 'Technical support specialist',
                'system_prompt': 'You are Alex, a knowledgeable technical support specialist.',
                'emotional_prompt': DEFAULT_EMOTIONAL_PROMPT,
                'model_id': 'llama-3.1-8b-instant',
                'voice_id': 'elise',  # FIXED: Use lowercase for consistency
                'stt_provider': 'groq',  # NEW: Add STT provider
                'chunk_size_bytes': 4096,  # NEW: Add chunk size configuration
                'streaming_mode': 'auto',  # NEW: Add streaming mode preference
                'interrupt_speech_duration': 3.0,
                'allow_interruptions': True,
                'interrupt_min_words': 0,  # NEW: Add missing field
                'preemptive_synthesis': True,  # NEW: Add missing field
                'initial_greeting': 'Hello my name is Alex, I\'m here to help with your technical questions.',
                'use_initial_greeting': True,
                'is_active': False
            }
        ]
        
        for agent in default_agents:
            agents_storage[agent['id']] = agent
        
        # Save default agents to persistent storage
        save_json_data(AGENTS_FILE, agents_storage)

# Initialize default agents on startup
initialize_default_agents()

def combine_prompts(emotional_prompt: str, system_prompt: str) -> str:
    """Combine emotional prompt with system prompt"""
    if not emotional_prompt:
        return system_prompt
    return f"{emotional_prompt}\n\n{system_prompt}"

def start_dynamic_agent_internal(agent_name: str, llm_prompt: str, agent_type: str = 'phone', agent_id: str = None):
    """Internal function to start a dynamic agent without HTTP request"""
    if not agent_name or not llm_prompt:
        return {"success": False, "error": "agent_name and llm_prompt are required"}

    logger.info(f"Starting dynamic agent internally: {agent_name} (type: {agent_type})")

    try:
        import re  # Import re module at the start of the function
        # Choose template based on agent type
        if agent_type == 'web':
            template_path = os.path.join(os.path.dirname(__file__), "web_agent_template.py")
        else:
            template_path = os.path.join(os.path.dirname(__file__), "orp.py")
            
        if not os.path.exists(template_path):
            logger.error(f"Template not found at {template_path}")
            return {"success": False, "error": f"Template not found: {template_path}"}

        with open(template_path, 'r', encoding='utf-8') as f:
            original_content = f.read()

        # Escape the incoming llm_prompt to be safely embedded in a Python triple-quoted string
        escaped_llm_prompt = llm_prompt.replace('"""', '\"\"\"')
        
        # Replace _system_prompt
        prompt_replacement_string = f'_system_prompt = """{escaped_llm_prompt}"""'
        new_content = re.sub(r'_system_prompt\s*=\s*r?"""(?:.|\n)*?"""', prompt_replacement_string, original_content, count=1)
        
        if new_content == original_content:
            logger.warning("System prompt replacement in agent template might have failed. Check the template structure.")

        # Replace user_identity
        target_identity_line = 'user_identity = "DYNAMIC_AGENT_USER_IDENTITY_PLACEHOLDER"'
        replacement_identity_line = f'user_identity = "{agent_name}"'
        if target_identity_line in new_content:
            new_content = new_content.replace(target_identity_line, replacement_identity_line)
        else:
            logger.warning(f"Target line '{target_identity_line}' for agent name replacement not found in template.")
        
        # 🔧 FIX: Also replace logger name with agent name for proper logging
        logger_placeholder = 'logging.getLogger("DYNAMIC_AGENT_USER_IDENTITY_PLACEHOLDER")'
        logger_replacement = f'logging.getLogger("{agent_name}")'
        if logger_placeholder in new_content:
            new_content = new_content.replace(logger_placeholder, logger_replacement)
            logger.info(f"✅ Replaced logger name with agent name: {agent_name}")
        else:
            # Fallback: replace any occurrence of the placeholder in logging contexts
            new_content = new_content.replace('DYNAMIC_AGENT_USER_IDENTITY_PLACEHOLDER', agent_name)
            logger.info(f"✅ Replaced all agent identity placeholders with: {agent_name}")

        # Replace agent_name placeholder in WorkerOptions
        worker_agent_name_placeholder = '"DYNAMIC_WORKER_AGENT_NAME_PLACEHOLDER"'
        actual_agent_name_quoted = f'"{agent_name}"'
        if worker_agent_name_placeholder in new_content:
            new_content = new_content.replace(worker_agent_name_placeholder, actual_agent_name_quoted)
            logger.info(f"Replaced worker agent name placeholder with {actual_agent_name_quoted}")
        else:
            logger.warning(f"Worker agent name placeholder '{worker_agent_name_placeholder}' not found in template.")

        # 🔧 NEW: Replace voice interaction settings placeholders with values from database
        if agent_id and agent_id in agents_storage:
            agent_data = agents_storage[agent_id]
            
            # Get voice interaction settings from database (with better defaults)
            interrupt_speech_duration = agent_data.get('interrupt_speech_duration', 2.0)  # Better default: 2 seconds
            interrupt_min_words = agent_data.get('interrupt_min_words', 2)               # Better default: 2 words
            allow_interruptions = agent_data.get('allow_interruptions', True)           # Dynamic toggle
            
            logger.info(f"🎙️ Configuring voice interaction settings: interrupt_duration={interrupt_speech_duration}s, interrupt_min_words={interrupt_min_words}, allow_interruptions={allow_interruptions}")
            
            # Replace the dynamic voice interaction placeholders
            voice_replacements = {
                'DYNAMIC_INTERRUPT_SPEECH_DURATION_PLACEHOLDER': str(interrupt_speech_duration),
                'DYNAMIC_INTERRUPT_MIN_WORDS_PLACEHOLDER': str(interrupt_min_words),
                'DYNAMIC_ALLOW_INTERRUPTIONS_PLACEHOLDER': str(allow_interruptions),
                # FIXED: Remove endpointing delays to prevent audio cutoffs (let LiveKit use defaults)
                'DYNAMIC_PREEMPTIVE_SYNTHESIS_PLACEHOLDER': 'True'   # Enable preemptive synthesis
            }
            
            for placeholder, value in voice_replacements.items():
                if placeholder in new_content:
                    new_content = new_content.replace(placeholder, value)
                    logger.info(f"✅ Replaced voice setting: {placeholder} -> {value}")
                else:
                    logger.warning(f"⚠️ Voice setting placeholder '{placeholder}' not found in template")
            
            # 🔧 NEW: Replace hardcoded greeting with agent's configured greeting
            initial_greeting = agent_data.get('initial_greeting', f'Hello my name is {agent_data.get("name", "Agent")}, how can I help you today?')
            use_initial_greeting = agent_data.get('use_initial_greeting', True)
            
            logger.info(f"🗨️ Configuring greeting: use_greeting={use_initial_greeting}, greeting='{initial_greeting}'")
            
            if use_initial_greeting:
                # Replace hardcoded greetings in both web_agent_template.py and orp.py
                custom_greeting_line = f'await agent.say("{initial_greeting}", allow_interruptions=True)'
                
                # Web template greeting
                web_greeting_line = 'await agent.say("Hello! I\'m here to help you with anything you need. What can I assist you with today?", allow_interruptions=True)'
                # Phone template greetings
                phone_greeting_line1 = 'await agent.say("Hello! How can I help you today?", allow_interruptions=True)'
                phone_greeting_line2 = 'await agent.say("Hi there!", allow_interruptions=True)'
                
                replaced_count = 0
                if web_greeting_line in new_content:
                    new_content = new_content.replace(web_greeting_line, custom_greeting_line)
                    replaced_count += 1
                    logger.info(f"✅ Replaced web template greeting with: '{initial_greeting}'")
                
                if phone_greeting_line1 in new_content:
                    new_content = new_content.replace(phone_greeting_line1, custom_greeting_line)
                    replaced_count += 1
                    logger.info(f"✅ Replaced phone template main greeting with: '{initial_greeting}'")
                
                if phone_greeting_line2 in new_content:
                    new_content = new_content.replace(phone_greeting_line2, custom_greeting_line)
                    replaced_count += 1
                    logger.info(f"✅ Replaced phone template fallback greeting with: '{initial_greeting}'")
                
                if replaced_count == 0:
                    # Fallback: try to find any greeting line pattern
                    greeting_pattern = r'await agent\.say\("(Hello|Hi)[^"]*", allow_interruptions=True\)'
                    matches = re.findall(greeting_pattern, new_content)
                    if matches:
                        new_content = re.sub(greeting_pattern, custom_greeting_line, new_content)
                        logger.info(f"✅ Replaced {len(matches)} greeting pattern(s) with: '{initial_greeting}'")
                    else:
                        logger.warning(f"⚠️ Could not find any hardcoded greetings to replace in template")
            else:
                # Remove/disable greetings if use_initial_greeting is False
                disabled_greeting_comment = '# Greeting disabled by agent configuration'
                
                # Web template greeting
                web_greeting_line = 'await agent.say("Hello! I\'m here to help you with anything you need. What can I assist you with today?", allow_interruptions=True)'
                # Phone template greetings
                phone_greeting_line1 = 'await agent.say("Hello! How can I help you today?", allow_interruptions=True)'
                phone_greeting_line2 = 'await agent.say("Hi there!", allow_interruptions=True)'
                
                disabled_count = 0
                if web_greeting_line in new_content:
                    new_content = new_content.replace(web_greeting_line, disabled_greeting_comment)
                    disabled_count += 1
                
                if phone_greeting_line1 in new_content:
                    new_content = new_content.replace(phone_greeting_line1, disabled_greeting_comment)
                    disabled_count += 1
                
                if phone_greeting_line2 in new_content:
                    new_content = new_content.replace(phone_greeting_line2, disabled_greeting_comment)
                    disabled_count += 1
                
                if disabled_count > 0:
                    logger.info(f"✅ Disabled {disabled_count} greeting(s) as per agent configuration")
                else:
                    # Fallback: try to find and comment out any greeting line pattern
                    greeting_pattern = r'(await agent\.say\("(Hello|Hi)[^"]*", allow_interruptions=True\))'
                    matches = re.findall(greeting_pattern, new_content)
                    if matches:
                        new_content = re.sub(greeting_pattern, rf'# \1  # Greeting disabled by agent configuration', new_content)
                        logger.info(f"✅ Commented out {len(matches)} greeting(s) as per agent configuration")
                    else:
                        logger.warning(f"⚠️ Could not find any hardcoded greetings to disable in template")
                    
        else:
            # Use default values if agent not found in storage
            logger.warning(f"⚠️ Agent '{agent_name}' not found in storage, using default voice interaction settings")
            default_voice_replacements = {
                'DYNAMIC_INTERRUPT_SPEECH_DURATION_PLACEHOLDER': '2.0',  # Better default
                'DYNAMIC_INTERRUPT_MIN_WORDS_PLACEHOLDER': '2',          # Better default
                'DYNAMIC_ALLOW_INTERRUPTIONS_PLACEHOLDER': 'True',      # Better default
                # FIXED: Remove endpointing delays to prevent audio cutoffs (let LiveKit use defaults)
                'DYNAMIC_PREEMPTIVE_SYNTHESIS_PLACEHOLDER': 'True'      # Enable preemptive synthesis
            }
            
            for placeholder, value in default_voice_replacements.items():
                if placeholder in new_content:
                    new_content = new_content.replace(placeholder, value)
                    
            # 🔧 NEW: Use default greeting when agent not found in storage
            default_greeting = f'Hello my name is {agent_name}, how can I help you today?'
            custom_greeting_line = f'await agent.say("{default_greeting}", allow_interruptions=True)'
            
            # Replace hardcoded greetings in both templates
            web_greeting_line = 'await agent.say("Hello! I\'m here to help you with anything you need. What can I assist you with today?", allow_interruptions=True)'
            phone_greeting_line1 = 'await agent.say("Hello! How can I help you today?", allow_interruptions=True)'
            phone_greeting_line2 = 'await agent.say("Hi there!", allow_interruptions=True)'
            
            replaced_count = 0
            if web_greeting_line in new_content:
                new_content = new_content.replace(web_greeting_line, custom_greeting_line)
                replaced_count += 1
                logger.info(f"✅ Replaced web template greeting with default: '{default_greeting}'")
            
            if phone_greeting_line1 in new_content:
                new_content = new_content.replace(phone_greeting_line1, custom_greeting_line)
                replaced_count += 1
                logger.info(f"✅ Replaced phone template main greeting with default: '{default_greeting}'")
            
            if phone_greeting_line2 in new_content:
                new_content = new_content.replace(phone_greeting_line2, custom_greeting_line)
                replaced_count += 1
                logger.info(f"✅ Replaced phone template fallback greeting with default: '{default_greeting}'")
            
            if replaced_count == 0:
                # Fallback: try to find any greeting line pattern
                greeting_pattern = r'await agent\.say\("(Hello|Hi)[^"]*", allow_interruptions=True\)'
                matches = re.findall(greeting_pattern, new_content)
                if matches:
                    new_content = re.sub(greeting_pattern, custom_greeting_line, new_content)
                    logger.info(f"✅ Replaced {len(matches)} greeting pattern(s) with default: '{default_greeting}'")
                else:
                    logger.warning(f"⚠️ Could not find any hardcoded greetings to replace in template")

        # 🔧 FIX: For all agents (web and phone), add comprehensive environment variable setting
        if agent_id and agent_id in agents_storage:
            agent_storage_data = agents_storage[agent_id]
            stt_provider = agent_storage_data.get('stt_provider', 'groq')
            voice_id = agent_storage_data.get('voice_id', 'elise')
            model_id = agent_storage_data.get('model_id', 'llama-3.3-70b-versatile')  # Get model_id from storage
            
            # FIXED: Extract chunk size from frontend streamingConfig
            streaming_config = agent_storage_data.get('streamingConfig', {})
            chunk_size_bytes = streaming_config.get('chunkSize', 4096)  # Use frontend value
            streaming_mode = agent_storage_data.get('streaming_mode', 'auto')
            
            # FIXED: Apply streaming preference to voice selection
            final_voice_id = voice_id
            if streaming_mode == 'streaming' and not voice_id.endswith('_async'):
                # Force streaming mode - use base voice ID
                final_voice_id = voice_id.replace('_async', '') if '_async' in voice_id else voice_id
            elif streaming_mode == 'non-streaming' and not voice_id.endswith('_async'):
                # Force non-streaming mode - add _async suffix
                final_voice_id = f"{voice_id}_async"
            # streaming_mode == 'auto': use voice_id as-is (default behavior)
            
            logger.info(f"🔧 Injecting environment variables: STT_PROVIDER={stt_provider}, VOICE_ID={final_voice_id}, MODEL_ID={model_id}, CHUNK_SIZE={chunk_size_bytes}, STREAMING_MODE={streaming_mode}")
            
            # 🔧 FIX: Replace hardcoded model in the LLM configuration
            hardcoded_model_line = 'model="llama-3.3-70b-versatile",'
            dynamic_model_line = f'model="{model_id}",'
            if hardcoded_model_line in new_content:
                new_content = new_content.replace(hardcoded_model_line, dynamic_model_line)
                logger.info(f"✅ Replaced hardcoded LLM model with: {model_id}")
            else:
                # Fallback: try to find any model= line in LLM configuration
                import re
                model_pattern = r'(model=")[^"]*(".*?# This is hardcoded!|",)'
                if re.search(model_pattern, new_content):
                    new_content = re.sub(model_pattern, rf'\1{model_id}\2', new_content)
                    logger.info(f"✅ Replaced LLM model pattern with: {model_id}")
                else:
                    # Try broader pattern for any Groq model configuration
                    groq_model_pattern = r'(model=")[^"]*(",.*?temperature=)'
                    if re.search(groq_model_pattern, new_content):
                        new_content = re.sub(groq_model_pattern, rf'\1{model_id}\2', new_content)
                        logger.info(f"✅ Replaced Groq model configuration with: {model_id}")
                    else:
                        logger.warning(f"⚠️ Could not find LLM model configuration to replace with: {model_id}")
            
            # Add environment variable settings at the top of the file
            env_settings = f'''
# Set environment variables for agent configuration
import os
os.environ['STT_PROVIDER'] = '{stt_provider}'
os.environ['VOICE_ID'] = '{final_voice_id}'
os.environ['MODEL_ID'] = '{model_id}'
os.environ['CHUNK_SIZE_BYTES'] = '{chunk_size_bytes}'
os.environ['STREAMING_MODE'] = '{streaming_mode}'

'''
            # Insert after the groq_stt import (more reliable anchor point)
            import_anchor = 'from groq_stt import GroqSTT'
            import_end = new_content.find(import_anchor)
            if import_end != -1:
                # Find the end of this line
                import_end = new_content.find('\n', import_end) + 1
                new_content = new_content[:import_end] + env_settings + new_content[import_end:]
                logger.info(f"✅ Environment variables injected after GroqSTT import")
            else:
                # Fallback: add after the "# Load environment variables" comment
                import_end = new_content.find('# Load environment variables')
                if import_end != -1:
                    # Find the beginning of this line
                    line_start = new_content.rfind('\n', 0, import_end) + 1
                    new_content = new_content[:line_start] + env_settings + new_content[line_start:]
                    logger.info(f"✅ Environment variables injected before 'Load environment variables' comment")
                else:
                    # Last fallback: add after import orpheus
                    import_end = new_content.find('import orpheus')
                    if import_end != -1:
                        import_end = new_content.find('\n', import_end) + 1
                        new_content = new_content[:import_end] + env_settings + new_content[import_end:]
                        logger.info(f"✅ Environment variables injected after orpheus import")
                    else:
                        logger.warning(f"⚠️ Could not find suitable anchor point for environment variable injection")
        else:
            logger.warning(f"⚠️ Agent type '{agent_type}' detected but agent_id '{agent_id}' not found in agents_storage or missing agent_id")
            
            # 🔧 FIX: Still replace hardcoded model with default for consistency
            default_model_id = "llama-3.3-70b-versatile"
            hardcoded_model_line = 'model="llama-3.3-70b-versatile",'
            dynamic_model_line = f'model="{default_model_id}",'
            if hardcoded_model_line in new_content:
                logger.info(f"✅ Confirmed default LLM model: {default_model_id}")
            else:
                # Try to find and set any model configuration to default
                import re
                groq_model_pattern = r'(model=")[^"]*(",.*?temperature=)'
                if re.search(groq_model_pattern, new_content):
                    new_content = re.sub(groq_model_pattern, rf'\1{default_model_id}\2', new_content)
                    logger.info(f"✅ Set LLM model to default: {default_model_id}")
                else:
                    logger.warning(f"⚠️ Could not find LLM model configuration to set default: {default_model_id}")

        safe_agent_filename = secure_filename(agent_name.replace(' ', '_')) + f"_{agent_type}.py"
        new_agent_filepath = os.path.join(DYNAMIC_AGENTS_DIR, safe_agent_filename)

        with open(new_agent_filepath, 'w', encoding='utf-8') as f:
            f.write(new_content)
        logger.info(f"Created dynamic agent script: {new_agent_filepath}")

        # Run the newly created agent script
        cmd = [sys.executable, new_agent_filepath, "dev"]
        process_cwd = os.path.dirname(__file__)
        process = subprocess.Popen(cmd, cwd=process_cwd)
        
        # 🔧 FIX: Use provided agent_id consistently, or generate name-based ID for new agents
        if not agent_id:
            # For new agents without existing ID, create name-based ID for consistency
            agent_id = agent_name.lower().replace(' ', '_').replace('-', '_')
            logger.info(f"Generated name-based agent ID: {agent_id} for agent: {agent_name}")
        else:
            # Use the existing agent_id passed from activation
            logger.info(f"Using existing agent ID: {agent_id} for agent: {agent_name}")
        
        # Get voice_id and model_id from agents_storage if agent_id is provided
        voice_id = "elise"
        model_id = "llama-3.3-70b-versatile"
        stt_provider = "groq"
        
        if agent_id and agent_id in agents_storage:
            agent_storage_data = agents_storage[agent_id]
            voice_id = agent_storage_data.get('voice_id', 'elise')
            model_id = agent_storage_data.get('model_id', 'llama-3.3-70b-versatile')
            stt_provider = agent_storage_data.get('stt_provider', 'groq')
            logger.info(f"Loaded agent config: voice={voice_id}, model={model_id}, stt={stt_provider}")
        
        # Register the dynamic agent in the running_agents system using the SAME agent_id
        running_agents[agent_id] = AgentProcess(
            agent_id=agent_id,
            agent_name=agent_name,
            system_prompt=llm_prompt,
            voice_id=voice_id,
            model_id=model_id,
            process=process,
            status="running"
        )
        
        # Also add to AGENT_CONFIG for compatibility using the SAME agent_id
        AGENT_CONFIG[agent_id] = {
            "name": agent_name,
            "voice_id": voice_id,
            "model_id": model_id,
            "system_prompt": llm_prompt
        }
        
        # Store the process for management
        running_dynamic_agents[agent_name] = process

        logger.info(f"✅ Successfully started dynamic agent '{agent_name}' with CONSISTENT ID {agent_id} and PID {process.pid}")
        return {
            "success": True,
            "message": f"Dynamic agent '{agent_name}' started successfully.",
            "agent_name": agent_name,
            "agent_id": agent_id,  # Return the SAME agent_id that was passed in
            "script_path": new_agent_filepath,
            "pid": process.pid,
            "agent_type": agent_type
        }

    except Exception as e:
        logger.error(f"Error starting dynamic agent '{agent_name}': {str(e)}", exc_info=True)
        return {"success": False, "error": f"Failed to start dynamic agent: {str(e)}"}

@app.route('/dynamic_agent/start', methods=['POST'])
def start_dynamic_agent():
    data = request.json
    agent_name = data.get('agent_name')
    llm_prompt = data.get('llm_prompt')
    agent_type = data.get('agent_type', 'phone')  # 'phone' or 'web'

    if not agent_name or not llm_prompt:
        return jsonify({"error": "agent_name and llm_prompt are required"}), 400

    logger.info(f"Received request to start dynamic agent: {agent_name} (type: {agent_type})")

    # 🔧 FIX: Find agent_id from agent_name to enable STT provider injection
    agent_id = None
    # First try exact match by name
    for aid, agent_data in agents_storage.items():
        if agent_data.get('name', '').lower() == agent_name.lower():
            agent_id = aid
            logger.info(f"✅ Found agent_id '{agent_id}' for agent '{agent_name}'")
            break
    
    # If not found, try name-based ID generation (for consistency)
    if not agent_id:
        potential_agent_id = agent_name.lower().replace(' ', '_').replace('-', '_')
        if potential_agent_id in agents_storage:
            agent_id = potential_agent_id
            logger.info(f"✅ Found agent_id '{agent_id}' via name-based ID for agent '{agent_name}'")
    
    if not agent_id:
        logger.warning(f"⚠️ Could not find agent_id for agent '{agent_name}' - STT provider will default to groq")

    result = start_dynamic_agent_internal(agent_name, llm_prompt, agent_type, agent_id)
    
    if result.get("success"):
        return jsonify(result), 200
    else:
        return jsonify(result), 500

@app.route('/make_call', methods=['POST'])
def make_call():
    data = request.json
    agent_name_for_dispatch = data.get('agent_name_for_dispatch') # e.g., "Mia" instead of "outbound-caller"
    phone_number = data.get('phone_number')
    agent_id = data.get('agent_id')  # Optional: if provided, look up the agent name

    if not phone_number:
        logger.error("Missing phone_number in /make_call request")
        return jsonify({"error": "phone_number is required"}), 400

    # Check if we need to create a phone agent or if agent name is already provided
    if agent_id and not agent_name_for_dispatch:
        agent_data = running_agents.get(agent_id)
        if agent_data:
            logger.info(f"Found agent '{agent_data.agent_name}' for phone call")
            
            # Create a phone agent dynamically for this call
            try:
                phone_agent_name = f"{agent_data.agent_name}_phone"
                logger.info(f"Creating phone agent '{phone_agent_name}' for phone call")
                
                # Create phone agent using the combined prompts (emotional + system)
                combined_prompt = combine_prompts(
                    agents_storage[agent_id].get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
                    agents_storage[agent_id].get('system_prompt', '')
                )
                phone_agent_response = start_dynamic_agent_internal(
                    agent_name=phone_agent_name,
                    llm_prompt=combined_prompt,
                    agent_type='phone',
                    agent_id=agent_id  # 🔧 FIX: Pass agent_id for STT provider injection
                )
                
                if phone_agent_response.get('success'):
                    # Use the phone agent name for dispatch
                    agent_name_for_dispatch = phone_agent_name
                    logger.info(f"Successfully created phone agent '{phone_agent_name}' for call")
                else:
                    logger.error(f"Failed to create phone agent: {phone_agent_response.get('error')}")
                    return jsonify({"error": "Failed to create phone agent for call"}), 500
                    
            except Exception as e:
                logger.error(f"Error creating phone agent: {str(e)}")
                return jsonify({"error": f"Failed to create phone agent: {str(e)}"}), 500
                
        else:
            # Try to get from AGENT_CONFIG as fallback
            agent_config = AGENT_CONFIG.get(agent_id)
            if agent_config:
                phone_agent_name = f"{agent_config['name']}_phone"
                # Use combined prompts from agents_storage if available, fallback to config
                if agent_id in agents_storage:
                    combined_prompt = combine_prompts(
                        agents_storage[agent_id].get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
                        agents_storage[agent_id].get('system_prompt', agent_config['system_prompt'])
                    )
                else:
                    combined_prompt = agent_config['system_prompt']  # Fallback for legacy configs
                phone_agent_response = start_dynamic_agent_internal(
                    agent_name=phone_agent_name,
                    llm_prompt=combined_prompt,
                    agent_type='phone',
                    agent_id=agent_id  # 🔧 FIX: Pass agent_id for STT provider injection
                )
                
                if phone_agent_response.get('success'):
                    agent_name_for_dispatch = phone_agent_name
                    logger.info(f"Created phone agent from config: '{phone_agent_name}'")
                else:
                    logger.error(f"Failed to create phone agent from config")
                    return jsonify({"error": "Failed to create phone agent for call"}), 500
            else:
                logger.error(f"Agent ID '{agent_id}' not found in running agents or config")
                return jsonify({"error": f"Agent ID '{agent_id}' not found"}), 400
    elif agent_name_for_dispatch:
        # If agent name is provided directly, check if it's already a phone agent
        if not agent_name_for_dispatch.endswith('_phone'):
            # Create phone version only if not already a phone agent
            phone_agent_name = f"{agent_name_for_dispatch}_phone"
            
            # Check if this phone agent already exists to avoid duplication
            existing_agent = None
            for agent_id, agent_process in running_agents.items():
                if agent_process.agent_name == phone_agent_name:
                    existing_agent = agent_process
                    break
            
            if existing_agent and existing_agent.status == "running":
                # Use existing phone agent
                agent_name_for_dispatch = phone_agent_name
                logger.info(f"Using existing phone agent: '{phone_agent_name}'")
            else:
                # Create new phone agent - get system prompt from campaign agent if possible
                default_prompt = "You are a helpful AI assistant making outbound phone calls."
                original_agent_id = None  # 🔧 FIX: Find agent_id for STT provider injection
                
                # Try to get the combined prompt from the original agent
                for aid, agent_process in running_agents.items():
                    if agent_process.agent_name == agent_name_for_dispatch:
                        # Use the already combined prompt from the running agent
                        default_prompt = agent_process.system_prompt
                        original_agent_id = aid  # 🔧 FIX: Store the agent_id
                        break
                
                # If not found in running agents, try agents_storage and combine prompts
                if not original_agent_id:
                    for aid, agent_data in agents_storage.items():
                        if agent_data.get('name', '').lower() == agent_name_for_dispatch.lower():
                            original_agent_id = aid
                            # Combine emotional and system prompts properly
                            default_prompt = combine_prompts(
                                agent_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
                                agent_data.get('system_prompt', default_prompt)
                            )
                            logger.info(f"✅ Found agent_id '{original_agent_id}' for phone agent creation")
                            break
                
                phone_agent_response = start_dynamic_agent_internal(
                    agent_name=phone_agent_name,
                    llm_prompt=default_prompt,
                    agent_type='phone',
                    agent_id=original_agent_id  # 🔧 FIX: Pass agent_id for STT provider injection
                )
                
                if phone_agent_response.get('success'):
                    agent_name_for_dispatch = phone_agent_name
                    logger.info(f"Created phone agent from name: '{phone_agent_name}'")
                else:
                    logger.error(f"Failed to create phone agent from name")
                    return jsonify({"error": "Failed to create phone agent for call"}), 500
        else:
            # 🔧 FIX: Agent name already ends with _phone, use it directly without modification
            logger.info(f"✅ Agent name already ends with '_phone', using directly: '{agent_name_for_dispatch}'")
    else:
        # Create default phone agent
        agent_name_for_dispatch = "outbound-caller"
        default_prompt = "You are a helpful AI assistant making outbound phone calls."
        
        phone_agent_response = start_dynamic_agent_internal(
            agent_name=agent_name_for_dispatch,
            llm_prompt=default_prompt,
            agent_type='phone',
            agent_id=None  # 🔧 FIX: No agent_id for default agent (will use groq STT)
        )
        
        if phone_agent_response.get('success'):
            logger.info(f"Created default phone agent: '{agent_name_for_dispatch}'")
        else:
            logger.warning("Failed to create default phone agent, using fallback")

    logger.info(f"Using phone agent '{agent_name_for_dispatch}' for phone call")

    # Get SIP trunk info if from_phone is provided
    from_phone = data.get('from_phone')
    sip_trunk_id = data.get('sip_trunk_id')
    
    # If from_phone is provided, get the appropriate SIP trunk
    if from_phone:
        sip_trunk_id = sip_trunk_mapping.get(from_phone, DEFAULT_SIP_TRUNK_ID)
        logger.info(f"Using SIP trunk {sip_trunk_id} for number {from_phone}")
    elif not sip_trunk_id:
        sip_trunk_id = DEFAULT_SIP_TRUNK_ID
        logger.info(f"Using default SIP trunk {sip_trunk_id}")

    # Build structured metadata for the agent
    if from_phone or sip_trunk_id != DEFAULT_SIP_TRUNK_ID:
        metadata = {
            "target_phone": phone_number,
            "from_phone": from_phone,
            "sip_trunk_id": sip_trunk_id,
            "campaign_id": data.get('campaign_id'),
            "call_id": data.get('call_id'),
            "contact_name": data.get('contact_name', 'Unknown')
        }
        metadata_str = json.dumps(metadata)
        logger.info(f"Using structured metadata: {metadata_str}")
    else:
        metadata_str = phone_number
        logger.info(f"Using simple metadata: {metadata_str}")

    cmd = [
        "lk", "dispatch", "create",
        "--new-room",
        "--agent-name", agent_name_for_dispatch,
        "--metadata", metadata_str
    ]
    
    process_cwd = os.path.dirname(__file__) # Directory of simple_backend.py

    try:
        logger.info(f"Executing command: {' '.join(cmd)} in CWD: {process_cwd}")
        result = subprocess.run(cmd, cwd=process_cwd, capture_output=True, text=True, check=False)

        if result.returncode == 0:
            logger.info(f"lk dispatch command successful. Output: {result.stdout}")
            
            # ✅ FIXED: Create call log entry for single calls (just like campaigns)
            call_log_entry = log_call({
                'contact_name': data.get('contact_name', 'Single Call'),
                'contact_phone': phone_number,
                'from_phone': from_phone,
                'agent_name': agent_name_for_dispatch,
                'campaign_id': '',  # Empty for single calls
                'status': 'initiated',
                'contact_notes': f'Single call initiated via /make_call endpoint',
                'call_id': f"single_call_{int(time.time())}"
            })
            
            logger.info(f"📋 Created call log for single call: {call_log_entry['contact_name']} ({call_log_entry['contact_phone']})")
            
            # Attempt to parse stdout as JSON if it looks like JSON, otherwise return as string
            try:
                output_json = json.loads(result.stdout)
            except json.JSONDecodeError:
                output_json = result.stdout.strip()
            return jsonify({"success": True, "message": "Call dispatched successfully.", "output": output_json, "call_log_id": call_log_entry['id']}), 200
        else:
            logger.error(f"lk dispatch command failed. Return code: {result.returncode}. Error: {result.stderr}. Output: {result.stdout}")
            return jsonify({
                "success": False, 
                "message": "Failed to dispatch call.", 
                "error": result.stderr.strip(), 
                "output": result.stdout.strip(),
                "returncode": result.returncode
            }), 500

    except FileNotFoundError:
        logger.error(f"lk command not found. Ensure LiveKit CLI is installed and in PATH.")
        return jsonify({"error": "lk command not found. Is LiveKit CLI installed and in PATH?"}), 500
    except Exception as e:
        logger.error(f"Error executing lk dispatch command: {e}", exc_info=True)
        return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500

# Dictionary to store running agents
running_agents = {}

# Dictionary to store call processes
call_processes = {}

# Directory for dynamically generated agent scripts
DYNAMIC_AGENTS_DIR = os.path.join(os.path.dirname(__file__), "dynamic_agents")
if not os.path.exists(DYNAMIC_AGENTS_DIR):
    os.makedirs(DYNAMIC_AGENTS_DIR)
    logger.info(f"Created dynamic_agents directory at {DYNAMIC_AGENTS_DIR}")

def cleanup_old_agent_files():
    """Clean up old agent files to prevent accumulation"""
    try:
        import time
        current_time = time.time()
        one_hour_ago = current_time - 3600  # 1 hour ago
        
        for filename in os.listdir(DYNAMIC_AGENTS_DIR):
            if filename.endswith('.py'):
                filepath = os.path.join(DYNAMIC_AGENTS_DIR, filename)
                file_mtime = os.path.getmtime(filepath)
                
                # Remove files older than 1 hour
                if file_mtime < one_hour_ago:
                    try:
                        os.remove(filepath)
                        logger.info(f"Cleaned up old agent file: {filename}")
                    except Exception as e:
                        logger.warning(f"Could not remove old agent file {filename}: {e}")
    except Exception as e:
        logger.warning(f"Error during agent cleanup: {e}")

# Run cleanup on startup
cleanup_old_agent_files()

# Dictionary to store running dynamic agents (optional, for future management)
running_dynamic_agents = {}

# Dictionary to store LiveKit workers
livekit_workers = {}

# 🔧 THREAD SAFETY: Add locks for concurrent access
import threading
agents_lock = threading.RLock()        # For running_agents access
livekit_lock = threading.RLock()       # For livekit_workers access  
calls_lock = threading.RLock()         # For call_processes access
storage_lock = threading.RLock()       # For agents_storage/campaigns_storage access

def get_free_port() -> int:
    """Get a free port for LiveKit worker"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        port = s.getsockname()[1]
        logger.info(f"Free port obtained for LiveKit worker: {port}")
        return port

def start_livekit_worker(agent_id: str, agent_name: str, system_prompt: str) -> Optional[int]:
    """Start a LiveKit worker for web calling"""
    try:
        # 🔧 THREAD SAFETY: Use lock for livekit_workers access
        with livekit_lock:
            # Check if we have an existing LiveKit worker for this agent
            if agent_id in livekit_workers:
                proc = livekit_workers[agent_id]
                if proc.is_alive():
                    logger.info(f"LiveKit worker already running for agent '{agent_name}' with PID {proc.pid}")
                    return None
                else:
                    # Clean up dead process
                    del livekit_workers[agent_id]

        # Get a free port for the worker
        port = get_free_port()
        
        # Find an available LiveKit worker script
        # We'll look for the current_livekit_voice.py script
        worker_script_path = None
        potential_paths = [
            os.path.join(os.path.dirname(__file__), "voice-agents", "current_livekit_voice.py"),
            os.path.join(os.path.dirname(__file__), "..", "voice-agents", "current_livekit_voice.py"),
            os.path.join("voice-agents", "current_livekit_voice.py")
        ]
        
        for path in potential_paths:
            if os.path.exists(path):
                worker_script_path = path
                break
        
        if not worker_script_path:
            logger.warning("LiveKit worker script not found. LiveKit web calling will not be available.")
            return None
        
        logger.info(f"Starting LiveKit worker for agent '{agent_name}' using script: {worker_script_path}")
        
        # Start the LiveKit worker process
        # Note: This is a simplified version - in production you'd want more robust process management
        cmd = [sys.executable, worker_script_path]
        env = os.environ.copy()
        env['WORKER_PORT'] = str(port)
        env['ASSISTANT_ID'] = agent_name
        env['SYSTEM_PROMPT'] = system_prompt
        env['ROOM_NAME'] = f'agent_room_{agent_id}'
        
        process = subprocess.Popen(
            cmd, 
            cwd=os.path.dirname(worker_script_path),
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 🔧 THREAD SAFETY: Store the process inside lock
        with livekit_lock:
            livekit_workers[agent_id] = process
        
        logger.info(f"Started LiveKit worker for agent '{agent_name}' with PID {process.pid} on port {port}")
        return port
        
    except Exception as e:
        logger.error(f"Error starting LiveKit worker for agent '{agent_name}': {str(e)}", exc_info=True)
        return None

def stop_livekit_worker(agent_id: str) -> bool:
    """Stop a LiveKit worker"""
    try:
        # 🔧 THREAD SAFETY: Use lock for livekit_workers access
        with livekit_lock:
            if agent_id in livekit_workers:
                proc = livekit_workers[agent_id]
                if proc.is_alive():
                    proc.terminate()
                    try:
                        proc.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        proc.kill()
                        proc.wait()
                del livekit_workers[agent_id]
                logger.info(f"Stopped LiveKit worker for agent ID '{agent_id}'")
                return True
            return False
    except Exception as e:
        logger.error(f"Error stopping LiveKit worker for agent ID '{agent_id}': {str(e)}", exc_info=True)
        return False

def load_env_variables():
    """Load environment variables from .env and .env.local files"""
    env_vars = {}
    
    # Try to load from .env.local first (takes precedence)
    if os.path.exists('.env.local'):
        with open('.env.local', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    try:
                        key, value = line.strip().split('=', 1)
                        env_vars[key] = value.strip('"').strip("'")
                    except ValueError:
                        continue
    
    # Then load from .env
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    try:
                        key, value = line.strip().split('=', 1)
                        # Only set if not already set from .env.local
                        if key not in env_vars:
                            env_vars[key] = value.strip('"').strip("'")
                    except ValueError:
                        continue
    
    # Update environment variables
    for key, value in env_vars.items():
        os.environ[key] = value
    
    return env_vars

@dataclass
class AgentProcess:
    agent_id: str
    agent_name: str
    system_prompt: str
    voice_id: str
    model_id: str
    process: Optional[subprocess.Popen] = None
    start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    logs: List[str] = field(default_factory=list)
    status: str = "initializing"

@dataclass
class CallProcess:
    call_id: str
    phone_number: str
    agent_id: str
    process: Optional[subprocess.Popen] = None
    start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    logs: List[str] = field(default_factory=list)
    status: str = "initializing"

def create_agent_file(agent_id, agent_name, system_prompt, voice_id, model_id, stt_provider="groq", initial_greeting=None):
    """Create a Python file for a specific agent with its parameters"""
    # Create the agents directory if it doesn't exist
    agents_dir = os.path.join(os.getcwd(), "agents")
    if not os.path.exists(agents_dir):
        os.makedirs(agents_dir)
        logger.info(f"Created agents directory at {agents_dir}")
    
    # Create the file path
    agent_file = os.path.join(agents_dir, f"agent_{agent_id}.py")
    
    # Escape any triple quotes in the system prompt to avoid syntax errors
    escaped_system_prompt = system_prompt.replace("'''", "\\'''")
    
    # Create the agent file content
    content = f'''import os
import sys
import time
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('agent_{agent_id}')

# Set environment variables for parameterized_orp.py to use
os.environ['AGENT_NAME'] = '{agent_name}'
os.environ['SYSTEM_PROMPT'] = """{escaped_system_prompt}"""
os.environ['VOICE_ID'] = '{voice_id}'
os.environ['MODEL_ID'] = '{model_id}'
os.environ['STT_PROVIDER'] = '{stt_provider}'

# Get the parent directory path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# This script runs parameterized_orp.py with specific parameters
if __name__ == '__main__':
    logger.info(f"Starting agent {agent_name} with voice {voice_id} and model {model_id}")
    
    # Build the command to run parameterized_orp.py directly
    cmd = [
        sys.executable,  # Python executable
        os.path.join(parent_dir, "parameterized_orp.py"),
        "dev",
        '--tts_voice', "{voice_id}",
        '--llm_model', "{model_id}",
        '--stt_provider', "{stt_provider}",
        "--agent_name", "{agent_name}"
    ]
    
    # Add system prompt if provided
    if """{escaped_system_prompt}""":
        cmd.extend(["--system_prompt", """{escaped_system_prompt}"""])
    
    # Add greeting if provided
    if """{initial_greeting}""":
        cmd.extend(["--greeting", """{initial_greeting}"""])
    else:
        cmd.extend(["--greeting", "Hello my name is {{agent_name}}, how can I help you today?"])
    
    logger.info(f"Running command: {{' '.join(cmd)}}")
    
    try:
        # Run parameterized_orp.py as a subprocess
        # We don't capture output so it will show in the terminal
        process = subprocess.Popen(
            cmd,
            cwd=parent_dir,
            env=os.environ.copy()
        )
        
        # Wait for the process to complete
        process.wait()
        logger.info("Agent process completed")
        
    except Exception as e:
        logger.error(f"Error running agent: {{str(e)}}")
    
    # Keep the terminal window open after the process completes
    logger.info("Keeping terminal window open. Press Ctrl+C to exit.")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Exiting...")
'''
    
    # Write the file
    with open(agent_file, 'w') as f:
        f.write(content)
    
    logger.info(f"Created agent file: {agent_file}")
    return agent_file

def build_agent_command(agent_id, agent_name, system_prompt, voice_id, model_id, stt_provider="groq"):
    """Build command to start an agent process"""
    # Get greeting from agent storage if available
    initial_greeting = None
    if agent_id in agents_storage:
        agent_data = agents_storage[agent_id]
        if agent_data.get('use_initial_greeting', True):
            initial_greeting = agent_data.get('initial_greeting')
    
    # Create the agent file
    agent_file = create_agent_file(agent_id, agent_name, system_prompt, voice_id, model_id, stt_provider, initial_greeting)
    
    # Build the command to run the agent file
    if os.name == 'nt':  # Windows
        # For Windows, open a new terminal window to run the agent
        # Set the window title to include the agent_id for easier process management
        return [
            "cmd.exe", "/c", "start", 
            f"Agent {agent_id} - {agent_name}",  # Window title
            "cmd.exe", "/k", 
            "python", agent_file, "dev"
        ]
    else:  # Linux/Mac
        # For Linux/Mac, use a terminal if available
        if os.system("which gnome-terminal > /dev/null 2>&1") == 0:
            return ["gnome-terminal", "--title", f"Agent {agent_id} - {agent_name}", "--", "python", agent_file, "dev"]
        elif os.system("which xterm > /dev/null 2>&1") == 0:
            return ["xterm", "-T", f"Agent {agent_id} - {agent_name}", "-e", f"python {agent_file} dev"]
        else:
            # Fallback to running without a terminal
            return ["python", agent_file, "dev"]

def build_call_command(phone_number, agent_name_descriptive):
    """Build command to start an outbound call based on user-provided syntax"""
    clean_number = phone_number.strip()
    return [
        "lk", "dispatch", "create",
        "--new-room",
        "--agent-name", agent_name_descriptive,  # Use the descriptive agent name (e.g., 'Mia')
        "--metadata", clean_number  # Phone number is passed via metadata
    ]

def read_process_output(process, logs, process_type):
    """Read and log process output"""
    for line in iter(process.stdout.readline, b''):
        try:
            line_str = line.decode('utf-8').strip()
            if line_str:
                timestamp = datetime.now().isoformat()
                log_entry = f"[{timestamp}] {line_str}"
                logs.append(log_entry)
                logger.info(f"{process_type} output: {line_str}")
        except Exception as e:
            logger.error(f"Error reading {process_type} output: {str(e)}")

def read_process_error(process, logs, process_type):
    """Read and log process errors"""
    for line in iter(process.stderr.readline, b''):
        try:
            line_str = line.decode('utf-8').strip()
            if line_str:
                timestamp = datetime.now().isoformat()
                log_entry = f"[{timestamp}] ERROR: {line_str}"
                logs.append(log_entry)
                logger.error(f"{process_type} error: {line_str}")
        except Exception as e:
            logger.error(f"Error reading {process_type} error output: {str(e)}")

@app.route('/api/agents', methods=['GET'])
def list_agents():
    """List all agents (both running and not running)"""
    global agents_storage
    try:
        # Force reload from database to ensure fresh data
        agents_storage = load_agents_from_db()
        logger.info(f"🔍 /api/agents endpoint - loaded {len(agents_storage)} agents from database")
        logger.info(f"🔍 Agent IDs in database: {list(agents_storage.keys())}")
        
        # Debug: Log agent IDs and running status
        running_agent_ids = list(running_agents.keys())
        logger.info(f"🏃 Running agents: {running_agent_ids}")
        
        agents = []
        
        # Add agents from storage
        for agent_id, agent_data in agents_storage.items():
            is_active = agent_id in running_agents
            
            # Ensure all required fields exist with defaults
            agent_response = {
                "id": agent_id,
                "name": agent_data.get('name', 'Unknown Agent'),
                "description": agent_data.get('description', ''),
                "systemPrompt": agent_data.get('system_prompt', ''),
                "emotionalPrompt": agent_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
                "modelId": agent_data.get('model_id', 'llama-3.3-70b-versatile'),
                "voiceId": agent_data.get('voice_id', 'tara'),  # FIXED: Use lowercase default
                "sttProvider": agent_data.get('stt_provider', 'groq'),  # Consistent field name
                "chunkSizeBytes": agent_data.get('chunk_size_bytes', 4096),  # NEW: Include chunk size
                "streamingMode": agent_data.get('streaming_mode', 'auto'),  # NEW: Include streaming mode
                "interruptSpeechDuration": agent_data.get('interrupt_speech_duration', 1.0),
                "allowInterruptions": agent_data.get('allow_interruptions', True),

                "interruptMinWords": agent_data.get('interrupt_min_words', 0),
                "preemptiveSynthesis": agent_data.get('preemptive_synthesis', True),
                "initialGreeting": agent_data.get('initial_greeting', f'Hello my name is {agent_data.get("name", "Agent")}, how can I help you today?'),
                "useInitialGreeting": agent_data.get('use_initial_greeting', True),
                "isActive": is_active,
                "createdAt": agent_data.get('created_at', ''),
            }
            
            agents.append(agent_response)
        
        return jsonify({"agents": agents})
    except Exception as e:
        logger.error(f"Error listing agents: {str(e)}")
        return jsonify({"error": str(e)}), 500

# New CRUD endpoints for agent management
@app.route('/api/agents', methods=['POST'])
def create_agent():
    """Create a new agent"""
    try:
        data = request.json
        if not data or not data.get('name') or not data.get('systemPrompt'):
            return jsonify({'error': 'Name and system prompt are required'}), 400
        
        # Use agent name as ID (consistent with mia, alex)
        agent_name_clean = data['name'].lower().replace(' ', '_')
        agent_id = agent_name_clean
        
        # Check if agent with this name already exists
        if agent_id in agents_storage:
            return jsonify({'error': f'Agent with name "{data["name"]}" already exists'}), 400
        
        # Create agent data with consistent field handling
        agent_data = {
            'id': agent_id,  # Store ID in data for consistency
            'name': data['name'],
            'description': data.get('description', ''),
            'system_prompt': data['systemPrompt'],
            'emotional_prompt': data.get('emotionalPrompt', DEFAULT_EMOTIONAL_PROMPT),
            'model_id': data.get('modelId', 'llama-3.3-70b-versatile'),  # Handle both field names
            'voice_id': data.get('voiceId', 'tara'),  # FIXED: Use lowercase default to match TTS system
            'stt_provider': data.get('sttProvider', data.get('stt_provider', 'groq')),  # Handle both field names
            'chunk_size_bytes': data.get('chunkSizeBytes', 4096),  # NEW: Handle chunk size configuration
            'streaming_mode': data.get('streamingMode', 'auto'),  # NEW: Handle streaming preference ('auto', 'streaming', 'non-streaming')
            'interrupt_speech_duration': data.get('interruptSpeechDuration', 1.0),
            'allow_interruptions': data.get('allowInterruptions', True),

            'interrupt_min_words': data.get('interruptMinWords', 0),
            'preemptive_synthesis': data.get('preemptiveSynthesis', True),
            'initial_greeting': data.get('initialGreeting', f'Hello my name is {data["name"]}, how can I help you today?'),
            'use_initial_greeting': data.get('useInitialGreeting', True),
            'is_active': False,
            'created_at': datetime.now().isoformat()
        }
        
        agents_storage[agent_id] = agent_data
        
        # Save to persistent storage (both JSON and database)
        save_json_data(AGENTS_FILE, agents_storage)
        save_agents_to_db(agents_storage)  # Also save to database
        
        return jsonify({'agent_id': agent_id, 'message': 'Agent created successfully'})
    except Exception as e:
        logger.error(f"Error creating agent: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/agents/<agent_id>', methods=['PUT'])
def update_agent(agent_id):
    """Update an existing agent"""
    try:
        if agent_id not in agents_storage:
            return jsonify({'error': 'Agent not found'}), 404
        
        data = request.json
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update agent data with proper field handling
        current_agent = agents_storage[agent_id]
        updated_data = {
            'name': data.get('name', current_agent.get('name', 'Unknown Agent')),
            'description': data.get('description', current_agent.get('description', '')),
            'system_prompt': data.get('systemPrompt', current_agent.get('system_prompt', '')),
            'emotional_prompt': data.get('emotionalPrompt', current_agent.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT)),
            'model_id': data.get('modelId', current_agent.get('model_id', 'llama-3.3-70b-versatile')),
            'voice_id': data.get('voiceId', current_agent.get('voice_id', 'tara')),  # FIXED: Use lowercase default
            'stt_provider': data.get('sttProvider', current_agent.get('stt_provider', 'groq')),
            'chunk_size_bytes': data.get('chunkSizeBytes', current_agent.get('chunk_size_bytes', 4096)),  # NEW: Handle chunk size
            'streaming_mode': data.get('streamingMode', current_agent.get('streaming_mode', 'auto')),  # NEW: Handle streaming preference
            'interrupt_speech_duration': data.get('interruptSpeechDuration', current_agent.get('interrupt_speech_duration', 1.0)),
            'allow_interruptions': data.get('allowInterruptions', current_agent.get('allow_interruptions', True)),


            'interrupt_min_words': data.get('interruptMinWords', current_agent.get('interrupt_min_words', 0)),
            'preemptive_synthesis': data.get('preemptiveSynthesis', current_agent.get('preemptive_synthesis', True)),
            'initial_greeting': data.get('initialGreeting', current_agent.get('initial_greeting', '')),
            'use_initial_greeting': data.get('useInitialGreeting', current_agent.get('use_initial_greeting', True)),
            'updated_at': datetime.now().isoformat()
        }
        
        # Keep original creation data
        updated_data['id'] = agent_id
        updated_data['created_at'] = current_agent.get('created_at', datetime.now().isoformat())
        updated_data['is_active'] = current_agent.get('is_active', False)
        
        agents_storage[agent_id] = updated_data
        
        # 🔧 FIX: Check if critical settings changed and restart agent if running
        critical_fields_changed = False
        if current_agent.get('model_id') != updated_data.get('model_id'):
            logger.info(f"🔄 Model ID changed from '{current_agent.get('model_id')}' to '{updated_data.get('model_id')}' for agent {agent_id}")
            critical_fields_changed = True
        if current_agent.get('voice_id') != updated_data.get('voice_id'):
            logger.info(f"🔄 Voice ID changed from '{current_agent.get('voice_id')}' to '{updated_data.get('voice_id')}' for agent {agent_id}")
            critical_fields_changed = True
        if current_agent.get('stt_provider') != updated_data.get('stt_provider'):
            logger.info(f"🔄 STT Provider changed from '{current_agent.get('stt_provider')}' to '{updated_data.get('stt_provider')}' for agent {agent_id}")
            critical_fields_changed = True
        
        # If agent is running and critical fields changed, restart it
        restart_message = ""
        if critical_fields_changed and agent_id in running_agents:
            logger.info(f"🔄 Restarting agent {agent_id} due to critical configuration changes")
            try:
                # Stop the current agent
                process = running_agents[agent_id].process
                if process:
                    process.terminate()
                    process.wait(timeout=5)
                del running_agents[agent_id]
                
                # Clean up AGENT_CONFIG
                if agent_id in AGENT_CONFIG:
                    del AGENT_CONFIG[agent_id]
                
                # Restart with new configuration
                combined_prompt = combine_prompts(
                    updated_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
                    updated_data['system_prompt']
                )
                
                result = start_dynamic_agent_internal(
                    agent_name=updated_data['name'],
                    llm_prompt=combined_prompt,
                    agent_type='phone',
                    agent_id=agent_id
                )
                
                if result.get('success'):
                    updated_data['is_active'] = True
                    restart_message = " Agent was automatically restarted with new configuration."
                    logger.info(f"✅ Successfully restarted agent {agent_id} with new configuration")
                else:
                    updated_data['is_active'] = False
                    restart_message = f" Warning: Failed to restart agent automatically: {result.get('error', 'Unknown error')}"
                    logger.error(f"❌ Failed to restart agent {agent_id}: {result.get('error')}")
                    
            except Exception as e:
                updated_data['is_active'] = False
                restart_message = f" Warning: Failed to restart agent automatically: {str(e)}"
                logger.error(f"❌ Error restarting agent {agent_id}: {str(e)}")
        
        # Save to persistent storage (including updated is_active status)
        save_json_data(AGENTS_FILE, agents_storage)
        save_agents_to_db(agents_storage)  # Also save to database
        
        return jsonify({'message': f'Agent updated successfully.{restart_message}', 'agent': {
            'id': agent_id,
            'name': agents_storage[agent_id]['name'],
            'description': agents_storage[agent_id]['description'],
            'systemPrompt': agents_storage[agent_id]['system_prompt'],
            'emotionalPrompt': agents_storage[agent_id]['emotional_prompt'],
            'modelId': agents_storage[agent_id]['model_id'],
            'voiceId': agents_storage[agent_id]['voice_id'],
            'sttProvider': agents_storage[agent_id]['stt_provider'],
            'chunkSizeBytes': agents_storage[agent_id].get('chunk_size_bytes', 4096),  # NEW: Return chunk size
            'streamingMode': agents_storage[agent_id].get('streaming_mode', 'auto'),  # NEW: Return streaming mode
            'interruptSpeechDuration': agents_storage[agent_id]['interrupt_speech_duration'],
            'allowInterruptions': agents_storage[agent_id]['allow_interruptions'],

            'interruptMinWords': agents_storage[agent_id]['interrupt_min_words'],
            'preemptiveSynthesis': agents_storage[agent_id]['preemptive_synthesis'],
            'initialGreeting': agents_storage[agent_id]['initial_greeting'],
            'useInitialGreeting': agents_storage[agent_id]['use_initial_greeting'],
            'isActive': agents_storage[agent_id]['is_active']
        }})
        
    except Exception as e:
        logger.error(f"Error updating agent: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/agents/<agent_id>', methods=['DELETE'])
def delete_agent(agent_id):
    """Delete an agent"""
    global agents_storage  # 🔧 FIX: Declare global at the start of function
    try:
        if agent_id not in agents_storage:
            return jsonify({'error': 'Agent not found'}), 404
        
        # 🔧 FIX: Comprehensive agent cleanup
        
        # 1. Stop the agent if it's active
        # 🔧 THREAD SAFETY: Use lock for running_agents access
        with agents_lock:
            if agent_id in running_agents:
                try:
                    process = running_agents[agent_id].process
                    if process:
                        process.terminate()
                        process.wait(timeout=5)
                    del running_agents[agent_id]
                    logger.info(f"Stopped running agent process for {agent_id}")
                except Exception as e:
                    logger.error(f"Error stopping agent process: {e}")
        
        # 2. Clean up AGENT_CONFIG if exists
        if agent_id in AGENT_CONFIG:
            del AGENT_CONFIG[agent_id]
            logger.info(f"Removed agent {agent_id} from AGENT_CONFIG")
        
        # 3. Stop any LiveKit workers
        try:
            stop_livekit_worker(agent_id)
        except Exception as e:
            logger.warning(f"Error stopping LiveKit worker for {agent_id}: {e}")
        
        # 4. Clean up dynamic agent files (optional - they'll be cleaned up automatically)
        try:
            agent_name = agents_storage[agent_id].get('name', 'Unknown')
            safe_agent_filename = secure_filename(agent_name.replace(' ', '_'))
            for agent_type in ['phone', 'web']:
                agent_file = os.path.join(DYNAMIC_AGENTS_DIR, f"{safe_agent_filename}_{agent_type}.py")
                if os.path.exists(agent_file):
                    os.remove(agent_file)
                    logger.info(f"Removed dynamic agent file: {agent_file}")
        except Exception as e:
            logger.warning(f"Error cleaning up agent files: {e}")
        
        # 5. Clean up running_dynamic_agents if exists
        agent_name = agents_storage[agent_id].get('name', 'Unknown')
        if agent_name in running_dynamic_agents:
            try:
                process = running_dynamic_agents[agent_name]
                if process and hasattr(process, 'terminate'):
                    process.terminate()
                del running_dynamic_agents[agent_name]
                logger.info(f"Cleaned up dynamic agent process for {agent_name}")
            except Exception as e:
                logger.warning(f"Error cleaning up dynamic agent: {e}")
        
        # Get agent name for logging before deletion
        agent_name = agents_storage[agent_id].get('name', 'Unknown')
        
        # 🔧 THREAD SAFETY: Use lock for agents_storage access
        with storage_lock:
            # Remove from in-memory storage
            del agents_storage[agent_id]
            
            # 🔧 FIX: Use specific deletion functions for both storage systems
            save_json_data(AGENTS_FILE, agents_storage)  # Update JSON file
            delete_agent_from_db(agent_id)  # Specifically delete from database
        
        # Debug: Verify deletion
        logger.info(f"✅ Agent '{agent_name}' (ID: {agent_id}) deleted successfully from both storage systems")
        logger.info(f"🔍 Remaining agents in storage: {list(agents_storage.keys())}")
        
        # Force verification by reloading from database and updating global storage
        verification_agents = load_agents_from_db()
        if agent_id in verification_agents:
            logger.error(f"❌ DELETION FAILED: Agent {agent_id} still exists in database after deletion!")
        else:
            logger.info(f"✅ DELETION VERIFIED: Agent {agent_id} confirmed removed from database")
            
        # 🔧 FIX: Update the global agents_storage to ensure consistency
        agents_storage = verification_agents
        logger.info(f"🔄 Updated global agents_storage, now contains: {list(agents_storage.keys())}")
        return jsonify({'message': 'Agent deleted successfully'})
    except Exception as e:
        logger.error(f"Error deleting agent: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/activate_agent', methods=['POST'])
def activate_agent():
    """Activate an agent"""
    try:
        data = request.json
        agent_id = data.get('agent_id')
        
        if not agent_id:
            return jsonify({"error": "Agent ID is required"}), 400
        
        if agent_id not in agents_storage:
            return jsonify({"error": "Agent not found"}), 404
        
        agent_data = agents_storage[agent_id]
        
        # Combine emotional prompt with system prompt
        combined_prompt = combine_prompts(
            agent_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
            agent_data['system_prompt']
        )
        
        # Create the dynamic agent
        result = start_dynamic_agent_internal(
            agent_name=agent_data['name'],
            llm_prompt=combined_prompt,
            agent_type='phone',
            agent_id=agent_id
        )
        
        if result.get('success'):
            agents_storage[agent_id]['is_active'] = True
            save_agents_to_db(agents_storage)  # 🔧 FIX: Persist activation status to database
            logger.info(f"✅ Agent {agent_data['name']} activated successfully with ID {agent_id}")
            return jsonify({"message": f"Agent {agent_data['name']} activated successfully"})
        else:
            return jsonify({"error": result.get('error', 'Failed to activate agent')}), 500
            
    except Exception as e:
        logger.error(f"Error activating agent: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/deactivate_agent', methods=['POST'])
def deactivate_agent():
    """Deactivate an agent"""
    try:
        data = request.json
        agent_id = data.get('agent_id')
        
        if not agent_id:
            return jsonify({"error": "Agent ID is required"}), 400
        
        if agent_id not in agents_storage:
            return jsonify({"error": "Agent not found"}), 404
        
        agent_data = agents_storage[agent_id]
        
        # Stop the agent process
        if agent_id in running_agents:
            try:
                process = running_agents[agent_id].process
                if process:
                    process.terminate()
                    process.wait(timeout=5)
                del running_agents[agent_id]
            except Exception as e:
                logger.error(f"Error stopping agent process: {e}")
        
        agents_storage[agent_id]['is_active'] = False
        save_agents_to_db(agents_storage)  # 🔧 FIX: Persist deactivation status to database
        logger.info(f"✅ Agent {agent_data['name']} deactivated successfully")
        return jsonify({"message": f"Agent {agent_data['name']} deactivated successfully"})
        
    except Exception as e:
        logger.error(f"Error deactivating agent: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/calls', methods=['POST'])
@app.route('/api/process', methods=['POST'])  # Support both endpoints as per memory
@app.route('/api/processes', methods=['POST'])  # Support both endpoints as per memory
def start_call():
    """Start a new outbound call"""
    try:
        # Log the raw request data for debugging
        logger.info(f"Received call request: {request.data}")
        
        data = request.get_json()
        logger.info(f"Parsed JSON data: {data}")
        
        if not data:
            logger.error("No data provided in request")
            return jsonify({"error": "No data provided"}), 400
        
        # Log all keys in the request data
        logger.info(f"Request data keys: {list(data.keys())}")
        
        phone_number = data.get('phoneNumber')
        if not phone_number:
            logger.error("Phone number is missing from request")
            return jsonify({"error": "Phone number is required"}), 400
        
        logger.info(f"Phone number from request: {phone_number}")
        
        agent_id = data.get('agentId')
        if not agent_id:
            logger.error("Agent ID is missing from request")
            return jsonify({"error": "Agent ID is required"}), 400
        
        logger.info(f"Agent ID from request: {agent_id}")
        
        # Check if agent is running
        agent_data = running_agents.get(agent_id)
        logger.info(f"Running agents: {list(running_agents.keys())}")
        
        if not agent_data:
            logger.error(f"Agent {agent_id} not found in running_agents")
            return jsonify({"error": f"Agent {agent_id} is not running. Please activate the agent first."}), 400
        
        if agent_data.status != "running":
            logger.error(f"Agent {agent_id} status is {agent_data.status}, not running")
            return jsonify({"error": f"Agent {agent_id} is not in running state (status: {agent_data.status}). Please activate the agent first."}), 400
        
        # Load environment variables
        env_vars = load_env_variables()
        
        # Validate required environment variables for LiveKit
        required_env_vars = [
            "LIVEKIT_URL", "LIVEKIT_API_KEY", "LIVEKIT_API_SECRET", 
            "SIP_OUTBOUND_TRUNK_ID"
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if var not in env_vars and var not in os.environ:
                missing_vars.append(var)
        
        if missing_vars:
            return jsonify({"error": f"Missing required environment variables: {', '.join(missing_vars)}"}), 400
        
        # Generate call ID
        call_id = str(uuid.uuid4())
        
        # Get the descriptive agent name from AGENT_CONFIG
        agent_config_details = AGENT_CONFIG.get(agent_id)
        if not agent_config_details:
            logger.error(f"Agent configuration for ID {agent_id} not found.")
            return jsonify({"error": f"Agent configuration for ID {agent_id} not found"}), 404
        descriptive_agent_name = agent_config_details['name']

        # Build call command - use the descriptive agent name for LiveKit
        cmd = build_call_command(phone_number, descriptive_agent_name)
        
        # Create call process object
        call_process = CallProcess(
            call_id=call_id,
            phone_number=phone_number,
            agent_id=agent_id
        )
        
        # Log the command we're about to run
        logger.info(f"Starting call with command: {' '.join(cmd)}")
        
        try:
            # Start the call process in a completely new terminal window
            if os.name == 'nt':  # Windows
                # For Windows, open a new terminal window with a descriptive title
                window_title = f"Call {call_id} - {phone_number} - Agent {agent_id}"
                cmd_str = " ".join(cmd)
                
                # Use start command to open a completely new terminal window
                terminal_cmd = [
                    "cmd.exe", "/c", "start", 
                    window_title,  # Window title
                    "cmd.exe", "/k", 
                    cmd_str  # The command as a single string
                ]
                
                logger.info(f"Running terminal command: {' '.join(terminal_cmd)}")
                
                # Execute the command to open a new terminal
                process = subprocess.Popen(
                    terminal_cmd,
                    env=os.environ.copy(),
                    cwd=os.getcwd(),
                    shell=False
                )
            else:  # Linux/Mac
                # For Linux/Mac, use a terminal if available
                cmd_str = " ".join(cmd)
                window_title = f"Call {call_id} - {phone_number} - Agent {agent_id}"
                
                if os.system("which gnome-terminal > /dev/null 2>&1") == 0:
                    terminal_cmd = [
                        "gnome-terminal", 
                        "--title", window_title,
                        "--", 
                        "bash", "-c", f"{cmd_str}; exec bash"
                    ]
                    process = subprocess.Popen(terminal_cmd, shell=False)
                elif os.system("which xterm > /dev/null 2>&1") == 0:
                    terminal_cmd = [
                        "xterm", 
                        "-title", window_title,
                        "-e", 
                        f"{cmd_str}; exec bash"
                    ]
                    process = subprocess.Popen(terminal_cmd, shell=False)
                else:
                    # Fallback to running without a terminal
                    logger.warning("No terminal emulator found, running without visible terminal")
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        env=os.environ.copy(),
                        cwd=os.getcwd()
                    )
                    
                    # Start threads to read output and error if we're not using a terminal
                    stdout_thread = threading.Thread(
                        target=read_process_output,
                        args=(process, call_process.logs, f"Call {call_id}"),
                        daemon=True
                    )
                    stderr_thread = threading.Thread(
                        target=read_process_error,
                        args=(process, call_process.logs, f"Call {call_id}"),
                        daemon=True
                    )
                    
                    stdout_thread.start()
                    stderr_thread.start()
            
            # Store the process and update status
            call_process.process = process
            call_process.status = "running"
            call_process.logs.append(f"Started call to {phone_number} using agent {agent_id}")
            
            # Add to call processes
            call_processes[call_id] = call_process
            
            logger.info(f"Call {call_id} started successfully")
            
            return jsonify({
                "callId": call_id,
                "phoneNumber": phone_number,
                "agentId": agent_id,
                "status": "running",
                "startTime": call_process.start_time.isoformat()
            })
            
        except Exception as e:
            error_msg = f"Error starting call {call_id}: {str(e)}"
            call_process.status = "failed"
            call_process.logs.append(error_msg)
            logger.error(error_msg)
            return jsonify({"error": error_msg}), 500
        
    except Exception as e:
        logger.error(f"Error starting call: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/calls/<call_id>', methods=['GET'])
def get_call(call_id):
    """Get details of a specific call"""
    try:
        call_data = call_processes.get(call_id)
        if not call_data:
            return jsonify({"error": "Call not found"}), 404
        
        return jsonify({
            "callId": call_id,
            "phoneNumber": call_data.phone_number,
            "agentId": call_data.agent_id,
            "status": call_data.status,
            "startTime": call_data.start_time.isoformat(),
            "logs": call_data.logs
        })
    except Exception as e:
        logger.error(f"Error getting call {call_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/processes', methods=['POST'])
def start_process():
    """Start a new process (single call) - Frontend compatibility endpoint"""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        phone_number = data.get('phoneNumber')
        agent_prompt = data.get('agentPrompt') 
        agent_id = data.get('agentId', 'custom')
        voice_id = data.get('voiceId', 'tara')
        model_id = data.get('modelId', 'llama-3.3-70b-versatile')
        
        if not phone_number:
            return jsonify({"error": "phoneNumber is required"}), 400
        
        if not agent_prompt:
            return jsonify({"error": "agentPrompt is required"}), 400
        
        logger.info(f"Starting single call process: {phone_number} with agent prompt")
        
        # Generate a unique process ID
        process_id = str(uuid.uuid4())
        
        # Create a temporary agent name for this call
        temp_agent_name = f"single_call_{process_id[:8]}"
        
        # Start dynamic agent for this single call
        result = start_dynamic_agent_internal(
            agent_name=temp_agent_name,
            llm_prompt=agent_prompt,
            agent_type='phone',
            agent_id=agent_id if agent_id != 'custom' else None
        )
        
        if not result.get('success'):
            logger.error(f"Failed to create agent for single call: {result.get('error')}")
            return jsonify({"error": f"Failed to create agent: {result.get('error', 'Unknown error')}"}), 500
        
        # Create call process object
        call_process = CallProcess(
            call_id=process_id,
            phone_number=phone_number,
            agent_id=agent_id
        )
        
        # Make the call using LiveKit dispatch
        cmd = [
            "lk", "dispatch", "create",
            "--new-room",
            "--agent-name", temp_agent_name,
            "--metadata", phone_number
        ]
        
        try:
            # Start the call process
            process_cwd = os.path.dirname(__file__)
            process = subprocess.Popen(
                cmd,
                cwd=process_cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=os.environ.copy()
            )
            
            # Store the process
            call_process.process = process
            call_process.status = "running"
            call_process.logs.append(f"Started call to {phone_number} using agent {temp_agent_name}")
            
            # Add to call processes
            call_processes[process_id] = call_process
            
            logger.info(f"✅ Single call process {process_id} started successfully")
            
            return jsonify({
                "processId": process_id,
                "status": "running",
                "startTime": call_process.start_time.isoformat()
            })
            
        except Exception as e:
            error_msg = f"Error starting call process: {str(e)}"
            logger.error(error_msg)
            return jsonify({"error": error_msg}), 500
        
    except Exception as e:
        logger.error(f"Error in start_process: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/processes', methods=['GET'])
def get_processes():
    """Get all processes (single calls) - Frontend compatibility endpoint"""
    try:
        processes = []
        
        for process_id, call_data in call_processes.items():
            # Check if process is still running
            current_status = call_data.status
            if call_data.process and current_status == "running":
                if call_data.process.poll() is not None:
                    current_status = "completed"
                    call_data.status = current_status
            
            processes.append({
                "id": process_id,
                "status": current_status,
                "startTime": call_data.start_time.isoformat(),
                "endTime": datetime.now().isoformat() if current_status != "running" else None,
                "callData": {
                    "phoneNumber": call_data.phone_number,
                    "voiceId": "tara",  # Default for compatibility
                    "modelId": "llama-3.3-70b-versatile"  # Default for compatibility
                },
                "logs": call_data.logs
            })
        
        return jsonify(processes)
        
    except Exception as e:
        logger.error(f"Error getting processes: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/processes/<process_id>', methods=['GET'])
def get_process_info(process_id):
    """Get details of a specific process - Frontend compatibility endpoint"""
    try:
        call_data = call_processes.get(process_id)
        if not call_data:
            return jsonify({"error": "Process not found"}), 404
        
        # Check if process is still running
        current_status = call_data.status
        if call_data.process and current_status == "running":
            if call_data.process.poll() is not None:
                current_status = "completed"
                call_data.status = current_status
        
        return jsonify({
            "id": process_id,
            "status": current_status,
            "startTime": call_data.start_time.isoformat(),
            "endTime": datetime.now().isoformat() if current_status != "running" else None,
            "callData": {
                "phoneNumber": call_data.phone_number,
                "voiceId": "tara",  # Default for compatibility
                "modelId": "llama-3.3-70b-versatile"  # Default for compatibility
            },
            "logs": call_data.logs
        })
        
    except Exception as e:
        logger.error(f"Error getting process {process_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/processes/<process_id>', methods=['DELETE'])
def stop_process(process_id):
    """Stop a running process - Frontend compatibility endpoint"""
    try:
        call_data = call_processes.get(process_id)
        if not call_data:
            return jsonify({"error": "Process not found"}), 404
        
        if call_data.process and call_data.status == "running":
            try:
                call_data.process.terminate()
                call_data.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                call_data.process.kill()
                call_data.process.wait()
            
            call_data.status = "stopped"
            call_data.logs.append(f"Process stopped at {datetime.now().isoformat()}")
        
        return jsonify({
            "processId": process_id,
            "status": call_data.status
        })
        
    except Exception as e:
        logger.error(f"Error stopping process {process_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/calls', methods=['GET'])
def list_calls():
    """List all calls"""
    try:
        calls = []
        
        for call_id, call_data in call_processes.items():
            calls.append({
                "callId": call_id,
                "phoneNumber": call_data.phone_number,
                "agentId": call_data.agent_id,
                "status": call_data.status,
                "startTime": call_data.start_time.isoformat()
            })
        
        return jsonify(calls)
    except Exception as e:
        logger.error(f"Error listing calls: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "runningAgents": len(running_agents),
        "activeCalls": len(call_processes)
    })

@app.route('/api/voices', methods=['GET'])
def list_voices():
    """Get list of available voices"""
    return get_available_voices()

@app.route('/api/models', methods=['GET'])
def list_models():
    """List available models"""
    models = [
        {"id": "llama-3.3-70b-versatile", "name": "Llama 3.3 70B", "description": "High performance model"},
        {"id": "llama-3.1-8b", "name": "Llama 3.1 8B", "description": "Fast, efficient model"},
        {"id": "mixtral-8x7b", "name": "Mixtral 8x7B", "description": "Powerful mixture-of-experts model"}
    ]
    return jsonify(models)

@app.route('/api/stt-providers', methods=['GET'])
def list_stt_providers():
    """List available STT providers"""
    providers = [
        {
            "id": "groq", 
            "name": "Groq Whisper", 
            "description": "Fast Whisper-large-v3-turbo model via Groq",
            "models": ["whisper-large-v3-turbo"]
        },
        {
            "id": "deepgram", 
            "name": "Deepgram", 
            "description": "High-quality speech recognition with low latency",
            "models": ["nova-2", "nova", "enhanced", "base"]
        }
    ]
    return jsonify(providers)

@app.route('/api/models/groq', methods=['GET'])
def get_groq_models():
    """Fetch available Groq models"""
    try:
        # Initialize Groq client
        groq_api_key = os.getenv('GROQ_API_KEY')
        if not groq_api_key:
            return jsonify({'error': 'GROQ_API_KEY not found in environment variables'}), 500
        
        client = Groq(api_key=groq_api_key)
        
        # Fetch models
        models_response = client.models.list()
        models = []
        
        for model in models_response.data:
            models.append({
                'id': model.id,
                'object': model.object,
                'created': model.created,
                'owned_by': model.owned_by,
                'context_window': getattr(model, 'context_window', None)
            })
        
        return jsonify({'models': models})
    except Exception as e:
        logger.error(f"Error fetching Groq models: {str(e)}")
        return jsonify({'error': f'Failed to fetch Groq models: {str(e)}'}), 500

@app.route('/api/livekit/spawn', methods=['POST'])
def spawn_livekit_worker():
    """Dispatch a LiveKit room for the agent (using existing LiveKit dispatch system)"""
    global agents_storage  # Declare global before any use
    try:
        data = request.json
        agent_id = data.get('agent_id')
        
        if not agent_id:
            return jsonify({"error": "agent_id is required"}), 400
        
        # 🔧 FIX: Enhanced agent lookup with better error handling
        logger.info(f"🔍 Looking for agent '{agent_id}'")
        
        # Force reload from database to ensure fresh data
        agents_storage = load_agents_from_db()
        logger.info(f"🔍 Loaded {len(agents_storage)} agents from database")
        
        # Debug: Show agent names for better debugging
        agent_names = {aid: data.get('name', 'Unknown') for aid, data in agents_storage.items()}
        logger.info(f"🔍 Available agents: {agent_names}")
            
        # Check if agent exists in agents_storage first
        if agent_id not in agents_storage:
            # Try to find agent by name-based ID generation (consistent with creation logic)
            found_agent_id = None
            search_name = None
            
            # First try exact name match from agent_names
            for aid, name in agent_names.items():
                if aid == agent_id or name.lower().replace(' ', '_') == agent_id:
                    found_agent_id = aid
                    search_name = name
                    break
            
            # If still not found, try case-insensitive name search
            if not found_agent_id:
                for aid, data in agents_storage.items():
                    agent_name = data.get('name', '').lower()
                    if agent_name and agent_name.replace(' ', '_') == agent_id.lower():
                        found_agent_id = aid
                        search_name = data.get('name', 'Unknown')
                        break
            
            if found_agent_id:
                # Use the correct agent ID
                original_agent_id = agent_id
                agent_id = found_agent_id
                logger.info(f"✅ Fixed agent ID mismatch: '{original_agent_id}' → '{agent_id}' for agent '{search_name}'")
            else:
                return jsonify({"error": f"Agent ID '{agent_id}' not found. Available agents: {agent_names}"}), 400
        
        agent_storage_data = agents_storage[agent_id]
        
        # Check if agent is running, if not try to activate it for web calling
        agent_data = running_agents.get(agent_id)
        if not agent_data:
            # Try to activate the agent for web calling
            logger.info(f"Agent {agent_id} not running, attempting to activate for web calling")
            
            # Combine emotional prompt with system prompt
            combined_prompt = combine_prompts(
                agent_storage_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
                agent_storage_data['system_prompt']
            )
            
            # Create the dynamic agent for web calling
            result = start_dynamic_agent_internal(
                agent_name=agent_storage_data['name'],
                llm_prompt=combined_prompt,
                agent_type='web',  # Use 'web' type for web calling
                agent_id=agent_id  # Pass the original agent_id
            )
            
            if not result.get('success'):
                return jsonify({"error": f"Failed to activate agent for web calling: {result.get('error', 'Unknown error')}"}), 500
            
            # Update agent status in database
            agents_storage[agent_id]['is_active'] = True
            save_agents_to_db(agents_storage)  # 🔧 FIX: Persist status to database
            logger.info(f"✅ Activated agent {agent_storage_data['name']} for web calling")
            
            # Get the agent data from running_agents after activation
            agent_data = running_agents.get(agent_id)
            if not agent_data:
                return jsonify({"error": f"Agent activation failed - not found in running agents"}), 500
        
        # 🔧 FIX: Use the unique room name provided by the frontend (from connection-details)
        # This ensures the agent joins the same isolated room as the user
        room_name = data.get('room_name')
        if not room_name:
            # Fallback: create a unique room name if not provided
            import time
            import random
            timestamp = int(time.time())
            session_id = random.randint(10000, 99999)
            room_name = f"agent_{agent_id}_fallback_{timestamp}_{session_id}"
            logger.warning(f"⚠️ No room_name provided, using fallback: {room_name}")
        
        # Use the actual agent name (not "outbound-caller") for LiveKit dispatch
        agent_identity = agent_data.agent_name  # This should match the agent you want
        
        cmd = [
            "lk", "dispatch", "create",
            "--room", room_name,
            "--agent-name", agent_identity,  # This MUST match the user_identity in the agent
            "--metadata", json.dumps({
                "agent_id": agent_id,
                "agent_name": agent_identity,
                "call_type": "web",
                "room_type": "agent_room"
            })
        ]
        
        logger.info(f"🎯 Using agent identity '{agent_identity}' for LiveKit dispatch")
        logger.info(f"🏠 Creating isolated room '{room_name}' for agent {agent_id}")
        logger.info(f"🔐 This ensures no agent collisions - each user gets their own private room")
        
        process_cwd = os.path.dirname(__file__)
        
        try:
            logger.info(f"Creating LiveKit room for web calling: {' '.join(cmd)}")
            result = subprocess.run(cmd, cwd=process_cwd, capture_output=True, text=True, check=False)
            
            if result.returncode == 0:
                logger.info(f"LiveKit room created successfully for agent {agent_data.agent_name}")
                return jsonify({
                    "message": f"LiveKit room ready for agent '{agent_data.agent_name}'",
                    "agent_id": agent_id,
                    "agent_name": agent_data.agent_name,
                    "room_name": room_name,
                    "status": "running"
                }), 200
            else:
                logger.error(f"Failed to create LiveKit room. Error: {result.stderr}")
                return jsonify({
                    "error": f"Failed to create LiveKit room: {result.stderr.strip()}"
                }), 500
                
        except FileNotFoundError:
            logger.error("lk command not found. Ensure LiveKit CLI is installed.")
            return jsonify({"error": "LiveKit CLI not found. Please ensure it's installed and in PATH."}), 500
        
    except Exception as e:
        logger.error(f"Error managing LiveKit room: {str(e)}", exc_info=True)
        return jsonify({"error": f"Failed to manage LiveKit room: {str(e)}"}), 500

@app.route('/api/connection-details', methods=['GET'])
def get_connection_details():
    """Get LiveKit connection details for web calling with proper user isolation"""
    try:
        # Get parameters
        agent_id = request.args.get('agentId')
        user_id = request.args.get('userId', 'anonymous')
        
        # Load environment variables
        livekit_url = os.getenv('LIVEKIT_URL')
        livekit_api_key = os.getenv('LIVEKIT_API_KEY')
        livekit_api_secret = os.getenv('LIVEKIT_API_SECRET')
        
        if not all([livekit_url, livekit_api_key, livekit_api_secret]):
            return jsonify({"error": "LiveKit configuration not found. Please set LIVEKIT_URL, LIVEKIT_API_KEY, and LIVEKIT_API_SECRET environment variables."}), 500
        
        # Import LiveKit token generation
        try:
            from livekit import api
            import time
            import random
            
            # 🔧 FIX: Create UNIQUE room per user-agent session
            timestamp = int(time.time())
            session_id = random.randint(10000, 99999)
            room_name = f"agent_{agent_id}_user_{user_id}_{timestamp}_{session_id}" if agent_id else f"web_call_{user_id}_{timestamp}_{session_id}"
            
            # 🔧 FIX: Create UNIQUE user identity  
            participant_identity = f"user_{user_id}_{timestamp}_{session_id}"
            participant_name = f"User {user_id}"
            
            logger.info(f"🏠 Creating isolated room: {room_name} for user {user_id} with agent {agent_id}")
            logger.info(f"👤 User identity: {participant_identity}")
            
            # Generate participant token with unique identity
            token = api.AccessToken(livekit_api_key, livekit_api_secret) \
                .with_identity(participant_identity) \
                .with_name(participant_name) \
                .with_grants(api.VideoGrants(
                    room_join=True,
                    room=room_name,
                    can_publish=True,
                    can_subscribe=True
                )).to_jwt()
            
            return jsonify({
                "serverUrl": livekit_url,
                "participantToken": token,
                "roomName": room_name,
                "participantIdentity": participant_identity,
                "agentId": agent_id,
                "userId": user_id
            })
            
        except ImportError:
            return jsonify({"error": "LiveKit SDK not available. Please install livekit-api."}), 500
            
    except Exception as e:
        logger.error(f"Error generating connection details: {str(e)}", exc_info=True)
        return jsonify({"error": f"Failed to generate connection details: {str(e)}"}), 500



@app.route('/api/tts/test', methods=['POST'])
def test_tts_endpoint():
    """Test TTS with provided text and voice"""
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400
        
    text = data.get('text')
    voice = data.get('voice', 'tara')
    
    return test_tts(text, voice)

@app.route('/api/tts/generate', methods=['POST'])
def generate_tts_endpoint():
    """Generate TTS audio for agents and return audio data"""
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400
        
    text = data.get('text')
    voice = data.get('voice', 'tara')
    
    return generate_tts(text, voice)

@app.route('/api/telnyx/numbers', methods=['GET'])
def get_telnyx_numbers():
    """Get available Telnyx phone numbers with SIP trunk mapping"""
    try:
        # Fetch available phone numbers from Telnyx
        phone_numbers = telnyx.PhoneNumber.list()
        
        numbers_data = []
        for number in phone_numbers.data:
            if number.status == 'active':
                numbers_data.append({
                    'phone_number': number.phone_number,
                    'connection_id': number.connection_id,
                    'messaging_profile_id': getattr(number, 'messaging_profile_id', None)
                })
        
        # Auto-create SIP trunks for new numbers and cleanup removed ones
        created_trunks = auto_create_sip_trunks_for_numbers(numbers_data)
        removed_numbers = cleanup_removed_numbers(numbers_data)
        
        # Build the response with SIP trunk information
        numbers = []
        for number in phone_numbers.data:
            if number.status == 'active':
                phone_num = number.phone_number
                sip_trunk_id = sip_trunk_mapping.get(phone_num, DEFAULT_SIP_TRUNK_ID)
                
                numbers.append({
                    'id': number.id,
                    'phone_number': phone_num,
                    'connection_id': number.connection_id,
                    'messaging_profile_id': getattr(number, 'messaging_profile_id', None),
                    'sip_trunk_id': sip_trunk_id,
                    'has_custom_trunk': phone_num in sip_trunk_mapping
                })
        
        return jsonify({
            'numbers': numbers,
            'count': len(numbers),
            'default_trunk_id': DEFAULT_SIP_TRUNK_ID,
            'auto_created_trunks': created_trunks,
            'removed_numbers': removed_numbers
        })
        
    except Exception as e:
        logger.error(f"Error fetching Telnyx numbers: {str(e)}")
        # Return mock data if Telnyx API fails
        mock_numbers = [
            {
                'id': '1', 
                'phone_number': '+1234567890', 
                'connection_id': 'conn_1',
                'sip_trunk_id': DEFAULT_SIP_TRUNK_ID,
                'has_custom_trunk': False
            },
            {
                'id': '2', 
                'phone_number': '+1234567891', 
                'connection_id': 'conn_2',
                'sip_trunk_id': DEFAULT_SIP_TRUNK_ID,
                'has_custom_trunk': False
            },
        ]
        return jsonify({
            'numbers': mock_numbers,
            'count': len(mock_numbers),
            'mock': True,
            'error': f'Using mock data: {str(e)}',
            'auto_created_trunks': []
        })

@app.route('/api/campaigns', methods=['GET'])
def get_campaigns():
    """Get all campaigns"""
    try:
        # Get fresh data from database
        fresh_campaigns = get_all_campaigns()
        campaigns_list = []
        
        for campaign_id, campaign_data in fresh_campaigns.items():
            # Ensure telnyxNumbers is always an array
            telnyx_numbers = campaign_data.get('telnyxNumbers', [])
            if isinstance(telnyx_numbers, str):
                try:
                    telnyx_numbers = json.loads(telnyx_numbers)
                except json.JSONDecodeError:
                    telnyx_numbers = []
            elif not isinstance(telnyx_numbers, list):
                telnyx_numbers = []
            
            campaigns_list.append({
                'id': campaign_id,
                'name': campaign_data.get('name', ''),
                'status': campaign_data.get('status', 'draft'),
                'telnyxNumbers': telnyx_numbers,  # Guaranteed to be an array
                'agentId': campaign_data.get('agent_id', ''),
                'agentName': campaign_data.get('agent_name', ''),
                'agentVoice': campaign_data.get('agent_voice', ''),
                'firstMessage': campaign_data.get('first_message', ''),
                'createdAt': campaign_data.get('created_at', ''),
                'contactsCount': len(campaign_data.get('contacts', []))
            })
        
        return jsonify(campaigns_list)
        
    except Exception as e:
        logger.error(f"Error fetching campaigns: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns', methods=['POST'])
def create_campaign():
    """Create a new campaign"""
    try:
        data = request.json
        
        # Generate campaign ID
        campaign_id = f"campaign_{int(datetime.now().timestamp())}"
        
        # Validate required fields
        required_fields = ['name', 'agentId']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Validate telnyxNumbers (plural) - at least one phone number is required
        if not data.get('telnyxNumbers') or len(data.get('telnyxNumbers', [])) == 0:
            return jsonify({'error': 'Missing required field: telnyxNumbers - at least one phone number is required'}), 400
        
        # Get agent data
        agent_id = data.get('agentId')
        agent_data = agents_storage.get(agent_id)
        if not agent_data:
            return jsonify({'error': f'Agent not found: {agent_id}'}), 400
        
        # Create campaign using agent data
        campaign = {
            'id': campaign_id,
            'name': data.get('name'),
            'status': 'draft',
            'telnyx_numbers': data.get('telnyxNumbers', []),  # Multiple numbers instead of single
            'agent_id': agent_id,
            'agent_name': agent_data.get('name'),
            'first_message': data.get('firstMessage', ''),
            'created_at': datetime.now().isoformat(),
            'contacts': data.get('contacts', []),
            # Store agent settings for easy access
            'agent_voice': agent_data.get('voice_id'),
            'agent_model': agent_data.get('model_id'),
            'agent_system_prompt': agent_data.get('system_prompt'),
            'agent_emotional_prompt': agent_data.get('emotional_prompt')
        }
        
        campaigns_storage[campaign_id] = campaign
        
        # Save to persistent storage
        save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        return jsonify({
            'message': 'Campaign created successfully',
            'campaign': {
                'id': campaign_id,
                'name': campaign['name'],
                'status': campaign['status'],
                'contactsCount': len(campaign['contacts'])
            }
        })
        
    except Exception as e:
        logger.error(f"Error creating campaign: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>', methods=['PUT'])
def update_campaign(campaign_id):
    """Update an existing campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        data = request.json
        campaign = campaigns_storage[campaign_id]
        
        # Update campaign fields
        updatable_fields = [
            'name', 'telnyx_numbers', 'system_message', 'first_message',
            'ai_provider', 'voice', 'custom_voice_id', 'language', 'contacts', 'agent_id'
        ]
        
        for field in updatable_fields:
            if field in data:
                if field == 'telnyxNumbers':
                    campaign['telnyx_numbers'] = data[field]
                elif field == 'systemMessage':
                    campaign['system_message'] = data[field]
                elif field == 'firstMessage':
                    campaign['first_message'] = data[field]
                elif field == 'aiProvider':
                    campaign['ai_provider'] = data[field]
                elif field == 'customVoiceId':
                    campaign['custom_voice_id'] = data[field]
                elif field == 'agentId':
                    campaign['agent_id'] = data[field]
                else:
                    campaign[field] = data[field]
        
        campaign['updated_at'] = datetime.now().isoformat()
        
        return jsonify({
            'message': 'Campaign updated successfully',
            'campaign': {
                'id': campaign_id,
                'name': campaign['name'],
                'status': campaign['status']
            }
        })
        
    except Exception as e:
        logger.error(f"Error updating campaign: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>', methods=['DELETE'])
def delete_campaign(campaign_id):
    """Delete a campaign"""
    try:
        # Use database instead of in-memory storage
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Check if campaign exists
            cursor.execute("SELECT id FROM campaigns WHERE id = ?", (campaign_id,))
            if not cursor.fetchone():
                return jsonify({'error': 'Campaign not found'}), 404
            
            # Delete associated call logs first
            cursor.execute("DELETE FROM call_logs WHERE campaign_id = ?", (campaign_id,))
        
            # Delete the campaign
            cursor.execute("DELETE FROM campaigns WHERE id = ?", (campaign_id,))
            conn.commit()
            
            logger.info(f"✅ Campaign deleted from database: {campaign_id}")
        return jsonify({'message': 'Campaign deleted successfully'})
        
    except Exception as e:
        logger.error(f"❌ Error deleting campaign: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/start', methods=['POST'])
def start_campaign(campaign_id):
    """Start a campaign with enhanced concurrent calling"""
    try:
        # 🔧 THREAD SAFETY: Use lock for campaigns_storage access
        with storage_lock:
            if campaign_id not in campaigns_storage:
                return jsonify({'error': 'Campaign not found'}), 404
            
            campaign = campaigns_storage[campaign_id]
            
            if not campaign.get('contacts'):
                return jsonify({'error': 'No contacts in campaign'}), 400
            
            # Check if campaign is already running
            if campaign.get('status') == 'running':
                return jsonify({'error': 'Campaign is already running'}), 400
            
            campaign['status'] = 'running'
            campaign['started_at'] = datetime.now().isoformat()
            
            # Save campaign status immediately
            save_campaigns_to_db(campaigns_storage)
        
        # Actually start calling the contacts in the campaign concurrently
        logger.info(f"🚀 Starting campaign {campaign_id} with {len(campaign['contacts'])} contacts - ENHANCED CONCURRENT CALLING")
        
        # Get the agent data once
        agent_data = agents_storage.get(campaign['agent_id'])
        if not agent_data:
            campaign['status'] = 'failed'
            campaign['error'] = f'Agent {campaign["agent_id"]} not found'
            save_campaigns_to_db(campaigns_storage)
            return jsonify({'error': f'Agent {campaign["agent_id"]} not found'}), 404
        
        # Enhanced thread-safe call statistics
        import threading
        from collections import defaultdict
        
        call_results = {
            'successful': 0,
            'failed': 0,
            'total': len(campaign['contacts']),
            'errors': [],
            'completed_contacts': []
        }
        results_lock = threading.Lock()
        
        def make_single_call(contact, campaign_phone_numbers, contact_index):
            """Enhanced single call function with better error handling and resource management"""
            thread_id = f"T{contact_index}"
            try:
                contact_name = contact.get('name', 'Customer')
                contact_phone = contact.get('phone_number')
                contact_notes = contact.get('notes', '')
                
                logger.info(f"📞 {thread_id}: Starting call to {contact_name} at {contact_phone}")
                
                # Validate phone number
                if not contact_phone or len(contact_phone) < 10:
                    error_msg = f"Invalid phone number: {contact_phone}"
                    logger.error(f"❌ {thread_id}: {error_msg}")
                    with results_lock:
                        call_results['failed'] += 1
                        call_results['errors'].append(f"{contact_name}: {error_msg}")
                    return
                
                # Distribute calls across multiple campaign phone numbers
                selected_phone = campaign_phone_numbers[contact_index % len(campaign_phone_numbers)]
                
                logger.info(f"📞 {thread_id}: Using outbound number {selected_phone} for {contact_name}")
                
                # Prepare enhanced call metadata
                call_metadata = {
                    'target_phone': contact_phone,
                    'from_phone': selected_phone,
                    'contact_name': contact_name,
                    'contact_notes': contact_notes,
                    'agent_name': agent_data['name'],
                    'agent_voice': agent_data.get('voice_id', 'Tara'),
                    'agent_model': agent_data.get('model_id', 'llama-3.3-70b-versatile'),
                    'system_prompt': agent_data.get('system_prompt', ''),
                    'emotional_prompt': agent_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
                    'campaign_id': campaign_id,
                    'first_message': campaign.get('first_message', ''),
                    'thread_id': thread_id,
                    'contact_index': contact_index
                }
                
                # Create personalized system prompt for this specific call
                base_prompt = agent_data.get('system_prompt', '')
                emotional_prompt = agent_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT)
                
                # 🔧 FIX: Use combine_prompts function for consistency
                combined_base_prompt = combine_prompts(emotional_prompt, base_prompt)
                
                # Build comprehensive contact database for the AI
                contact_database = []
                contact_database.append(f"Full Name: {contact_name}")
                
                if contact.get('firstName'):
                    contact_database.append(f"First Name: {contact.get('firstName')}")
                if contact.get('lastName'):
                    contact_database.append(f"Last Name: {contact.get('lastName')}")
                if contact.get('gender'):
                    contact_database.append(f"Preferred Title: {contact.get('gender')}")
                if contact.get('email'):
                    contact_database.append(f"Email: {contact.get('email')}")
                if contact.get('company'):
                    contact_database.append(f"Company: {contact.get('company')}")
                if contact_notes:
                    contact_database.append(f"Notes: {contact_notes}")
                
                contact_info = "\n".join(contact_database)
                
                # Now add contact personalization to the properly combined prompt
                personalized_prompt = f"""{combined_base_prompt}

=== CONTACT DATABASE ===
You are speaking with this person. Use this information to personalize your conversation appropriately:

{contact_info}

Instructions for using this information:
- Use their first name when it feels natural and appropriate in conversation
- Be aware of their company context if mentioned
- Reference any relevant notes to guide the conversation
- Address them respectfully using their preferred title if provided
- Keep the conversation natural - don't robotically recite their information"""
                
                # Create a unique temporary agent for this specific call
                import time
                import random
                timestamp = int(time.time())
                random_id = random.randint(1000, 9999)
                clean_contact_name = contact_name.replace(' ', '_').replace('.', '').replace('-', '_')[:10]
                temp_agent_name = f"{agent_data['name']}_{clean_contact_name}_{contact_index}_{timestamp}_{random_id}"
                
                logger.info(f"🤖 {thread_id}: Creating unique agent '{temp_agent_name}' for {contact_name}")
                
                # Start dynamic agent for this call with timeout
                try:
                    result = start_dynamic_agent_internal(
                        agent_name=temp_agent_name,
                        llm_prompt=personalized_prompt,
                        agent_type='phone',
                        agent_id=campaign['agent_id']  # 🔧 FIX: Pass campaign agent_id for STT provider injection
                    )
                    
                    if not result.get('success'):
                        error_msg = f"Failed to create agent: {result.get('error', 'Unknown error')}"
                        logger.error(f"❌ {thread_id}: {error_msg}")
                        with results_lock:
                            call_results['failed'] += 1
                            call_results['errors'].append(f"{contact_name}: {error_msg}")
                        return
                    
                    # Brief wait for agent to initialize
                    time.sleep(0.5)
                    
                    # Make the actual call using existing LiveKit system
                    logger.info(f"📞 {thread_id}: Dispatching LiveKit call for {contact_name}")
                    
                    call_data = {
                        'agent_name_for_dispatch': temp_agent_name,
                        'phone_number': contact_phone,
                        'agent_id': result.get('agent_id'),
                        'from_phone': selected_phone,
                        'sip_trunk_id': sip_trunk_mapping.get(selected_phone, DEFAULT_SIP_TRUNK_ID),
                        'campaign_id': campaign_id,
                        'call_id': f"call_{campaign_id}_{contact_index}_{timestamp}_{random_id}",
                        'contact_name': contact_name
                    }
                    
                    # Make the call request with extended timeout and retry logic
                    import requests
                    max_retries = 2
                    retry_delay = 1
                    
                    for attempt in range(max_retries):
                        try:
                            logger.info(f"🌐 {thread_id}: Making HTTP request (attempt {attempt + 1}/{max_retries})")
                            response = requests.post(
                                'http://localhost:9090/make_call', 
                                json=call_data
                                # REMOVED 10-second timeout - let call initiation complete
                            )
                            
                            if response.status_code == 200:
                                logger.info(f"✅ {thread_id}: LIVE CALL INITIATED successfully for {contact_name}")
                                
                                # Log successful call initiation (not answered yet)
                                call_id = log_call({
                                    'contact_name': contact_name,
                                    'contact_phone': contact_phone,
                                    'from_phone': selected_phone,
                                    'agent_name': agent_data['name'],
                                    'campaign_id': campaign_id,
                                    'status': 'initiated',  # Just initiated, not answered
                                    'contact_notes': contact_notes,
                                    'agent_instance': temp_agent_name,
                                    'call_id': call_data['call_id']
                                })
                                
                                                                 # Call status will be updated via webhook - no need for delayed checking
                                
                                with results_lock:
                                    call_results['successful'] += 1
                                    call_results['completed_contacts'].append({
                                        'name': contact_name,
                                        'phone': contact_phone,
                                        'status': 'initiated',  # Call dispatched, waiting for answer
                                        'agent': temp_agent_name,
                                        'call_id': call_data['call_id']
                                    })
                                
                                # Let call establish naturally - no blocking wait
                                logger.info(f"⏳ {thread_id}: Call dispatched successfully, will check status later")
                                return
                                
                            else:
                                error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                                logger.warning(f"⚠️ {thread_id}: Call attempt {attempt + 1} failed: {error_msg}")
                                
                                if attempt < max_retries - 1:
                                    time.sleep(retry_delay)
                                    retry_delay *= 2  # Exponential backoff
                                else:
                                    # Final failure
                                    logger.error(f"❌ {thread_id}: All call attempts failed for {contact_name}")
                                    
                                    log_call({
                                        'contact_name': contact_name,
                                        'contact_phone': contact_phone,
                                        'from_phone': selected_phone,
                                        'agent_name': agent_data['name'],
                                        'campaign_id': campaign_id,
                                        'status': 'failed',
                                        'contact_notes': contact_notes,
                                        'error': error_msg
                                    })
                                    
                                    with results_lock:
                                        call_results['failed'] += 1
                                        call_results['errors'].append(f"{contact_name}: {error_msg}")
                                        call_results['completed_contacts'].append({
                                            'name': contact_name,
                                            'phone': contact_phone,
                                            'status': 'failed',
                                            'error': error_msg
                                        })
                        
                        # REMOVED timeout error handling - no timeouts to handle anymore
                        
                        except requests.exceptions.RequestException as req_error:
                            error_msg = f"Request error: {str(req_error)}"
                            logger.warning(f"⚠️ {thread_id}: Attempt {attempt + 1} failed: {error_msg}")
                            if attempt == max_retries - 1:
                                with results_lock:
                                    call_results['failed'] += 1
                                    call_results['errors'].append(f"{contact_name}: {error_msg}")
                
                except Exception as agent_error:
                    error_msg = f"Agent creation error: {str(agent_error)}"
                    logger.error(f"❌ {thread_id}: {error_msg}")
                    with results_lock:
                        call_results['failed'] += 1
                        call_results['errors'].append(f"{contact_name}: {error_msg}")
        
            except Exception as e:
                error_msg = f"Unexpected error: {str(e)}"
                logger.error(f"❌ {thread_id}: {error_msg}", exc_info=True)
                with results_lock:
                    call_results['failed'] += 1
                    call_results['errors'].append(f"{contact.get('name', 'Unknown')}: {error_msg}")
        
        def process_campaign_calls():
            """Enhanced campaign processing with better monitoring and resource management"""
            try:
                # 🔧 FIX: Get phone numbers from both possible fields and ensure it's a list
                campaign_phone_numbers = campaign.get('telnyxNumbers', campaign.get('telnyx_numbers', []))
                
                # Ensure it's a list, not a string
                if isinstance(campaign_phone_numbers, str):
                    try:
                        campaign_phone_numbers = json.loads(campaign_phone_numbers)
                    except json.JSONDecodeError:
                        campaign_phone_numbers = [campaign_phone_numbers] if campaign_phone_numbers else []
                elif not isinstance(campaign_phone_numbers, list):
                    campaign_phone_numbers = []
                
                if not campaign_phone_numbers:
                    error_msg = 'No phone numbers configured'
                    logger.error(f"❌ Campaign {campaign_id}: {error_msg}")
                    campaign['status'] = 'failed'
                    campaign['error'] = error_msg
                    save_campaigns_to_db(campaigns_storage)
                    return
                
                logger.info(f"📞 Campaign {campaign_id}: Using {len(campaign_phone_numbers)} phone numbers: {', '.join(campaign_phone_numbers)}")
                
                # Create and start all threads
                threads = []
                for index, contact in enumerate(campaign['contacts']):
                    thread = threading.Thread(
                        target=make_single_call, 
                        args=(contact, campaign_phone_numbers, index),
                        name=f"CallThread-{index}-{contact.get('name', 'Unknown')}"
                    )
                    thread.daemon = False  # Ensure threads can complete
                    threads.append(thread)
                
                # Start all calls simultaneously
                start_time = time.time()
                logger.info(f"🚀 Campaign {campaign_id}: Starting {len(threads)} concurrent calls")
                
                for i, thread in enumerate(threads):
                    thread.start()
                    logger.info(f"🧵 Started thread {i}: {thread.name}")
                
                # Monitor thread completion - NO TIMEOUT, wait indefinitely
                logger.info(f"⏳ Campaign {campaign_id}: Waiting for all calls to complete (no timeout)")
                
                # Wait for all threads without timeout - let calls complete naturally
                for i, thread in enumerate(threads):
                    thread.join()  # REMOVED timeout - wait indefinitely for each thread
                    logger.info(f"✅ Thread {i} ({thread.name}) completed")
                
                # Final campaign status update
                total_time = time.time() - start_time
                campaign['status'] = 'completed'
                campaign['completed_at'] = datetime.now().isoformat()
                campaign['total_duration'] = round(total_time, 2)
                campaign['call_results'] = call_results
                
                logger.info(f"✅ Campaign {campaign_id} completed in {total_time:.1f}s:")
                logger.info(f"   📊 Successful: {call_results['successful']}/{call_results['total']}")
                logger.info(f"   📊 Failed: {call_results['failed']}/{call_results['total']}")
                
                if call_results['errors']:
                    logger.warning(f"   ⚠️ Errors encountered:")
                    for error in call_results['errors'][:5]:  # Show first 5 errors
                        logger.warning(f"     - {error}")
                
                # Save final campaign status
                save_campaigns_to_db(campaigns_storage)
                
            except Exception as e:
                error_msg = f"Campaign processing error: {str(e)}"
                logger.error(f"❌ Campaign {campaign_id}: {error_msg}", exc_info=True)
                campaign['status'] = 'failed'
                campaign['error'] = error_msg
                campaign['completed_at'] = datetime.now().isoformat()
                save_campaigns_to_db(campaigns_storage)
        
        # Start the enhanced concurrent calling process
        calling_thread = threading.Thread(target=process_campaign_calls, name=f"CampaignThread-{campaign_id}")
        calling_thread.daemon = False
        calling_thread.start()
        
        return jsonify({
            'message': 'Enhanced campaign started successfully - calls being initiated concurrently',
            'campaign': {
                'id': campaign_id,
                'status': campaign['status'],
                'contactsCount': len(campaign['contacts']),
                'phoneNumbers': campaign.get('telnyxNumbers', campaign.get('telnyx_numbers', [])),
                'features': [
                    'Concurrent calling',
                    'Unique agent per call',
                    'Distributed phone numbers',
                    'Enhanced error handling',
                    'Call retry logic'
                ]
            }
        })
        
    except Exception as e:
        logger.error(f"Error starting enhanced campaign: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/stop', methods=['POST'])
def stop_campaign(campaign_id):
    """Stop a campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        campaign['status'] = 'stopped'
        campaign['stopped_at'] = datetime.now().isoformat()
        
        return jsonify({
            'message': 'Campaign stopped successfully',
            'campaign': {
                'id': campaign_id,
                'status': campaign['status']
            }
        })
        
    except Exception as e:
        logger.error(f"Error stopping campaign: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sip-trunks', methods=['GET'])
def get_sip_trunk_mapping():
    """Get the current SIP trunk mapping"""
    return jsonify({
        'mapping': sip_trunk_mapping,
        'default_trunk_id': DEFAULT_SIP_TRUNK_ID
    })

@app.route('/api/sip-trunks', methods=['POST'])
def update_sip_trunk_mapping():
    """Update SIP trunk mapping for a phone number"""
    try:
        data = request.json
        phone_number = data.get('phone_number')
        sip_trunk_id = data.get('sip_trunk_id')
        
        if not phone_number or not sip_trunk_id:
            return jsonify({'error': 'phone_number and sip_trunk_id are required'}), 400
        
        sip_trunk_mapping[phone_number] = sip_trunk_id
        
        return jsonify({
            'message': 'SIP trunk mapping updated successfully',
            'phone_number': phone_number,
            'sip_trunk_id': sip_trunk_id
        })
        
    except Exception as e:
        logger.error(f"Error updating SIP trunk mapping: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sip-trunks/auto-create', methods=['POST'])
def auto_create_sip_trunk():
    """Automatically create SIP trunk and update mapping"""
    try:
        data = request.json
        phone_number = data.get('phone_number')
        trunk_config = data.get('trunk_config')
        
        if not phone_number or not trunk_config:
            return jsonify({'error': 'phone_number and trunk_config are required'}), 400
        
        logger.info(f"🚀 Auto-creating SIP trunk for {phone_number}")
        
        # Create temporary trunk config file
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(trunk_config, f, indent=2)
            temp_file = f.name
        
        try:
            # Run lk sip outbound create command
            cmd = ["lk", "sip", "outbound", "create", temp_file]
            logger.info(f"Running command: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)
            
            if result.returncode == 0:
                # Parse the output to get SIP trunk ID
                output = result.stdout.strip()
                logger.info(f"LiveKit SIP trunk creation output: {output}")
                
                # Try to extract SIP trunk ID from output
                sip_trunk_id = None
                try:
                    # Parse JSON output if possible
                    output_json = json.loads(output)
                    sip_trunk_id = output_json.get('sipTrunkId') or output_json.get('id')
                except json.JSONDecodeError:
                    # Try to extract from text output
                    import re
                    match = re.search(r'ST_[a-zA-Z0-9]+', output)
                    if match:
                        sip_trunk_id = match.group(0)
                
                if sip_trunk_id:
                    # Update the SIP trunk mapping
                    sip_trunk_mapping[phone_number] = sip_trunk_id
                    
                    # Save to persistent storage
                    save_json_data(SIP_TRUNKS_FILE, sip_trunk_mapping)
                    
                    logger.info(f"✅ Successfully created SIP trunk {sip_trunk_id} for {phone_number}")
                    
                    return jsonify({
                        'success': True,
                        'message': f'SIP trunk created successfully for {phone_number}',
                        'phone_number': phone_number,
                        'sip_trunk_id': sip_trunk_id,
                        'livekit_output': output
                    })
                else:
                    logger.error(f"Could not extract SIP trunk ID from output: {output}")
                    return jsonify({
                        'success': False,
                        'error': 'Could not extract SIP trunk ID from LiveKit output',
                        'livekit_output': output
                    }), 500
            else:
                logger.error(f"LiveKit command failed: {result.stderr}")
                return jsonify({
                    'success': False,
                    'error': 'Failed to create SIP trunk in LiveKit',
                    'livekit_error': result.stderr,
                    'livekit_output': result.stdout
                }), 500
                
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file):
                os.unlink(temp_file)
        
    except Exception as e:
        logger.error(f"Error auto-creating SIP trunk: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/call', methods=['POST'])
def start_campaign_call(campaign_id):
    """Start a call for a specific campaign using LiveKit dispatch"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        data = request.json
        target_phone = data.get('target_phone')
        
        if not target_phone:
            return jsonify({'error': 'target_phone is required'}), 400
        
        campaign = campaigns_storage[campaign_id]
        campaign_phone = campaign.get('telnyx_number')
        
        # Get the appropriate SIP trunk ID for this phone number
        sip_trunk_id = sip_trunk_mapping.get(campaign_phone, DEFAULT_SIP_TRUNK_ID)
        
        # Here you would integrate with your LiveKit calling system
        # Following the pattern from your existing guide:
        
        # 1. Create a new room
        # 2. Dispatch agent with the correct SIP trunk ID
        # 3. Use the campaign's system message and voice settings
        
        call_data = {
            'campaign_id': campaign_id,
            'target_phone': target_phone,
            'from_phone': campaign_phone,
            'sip_trunk_id': sip_trunk_id,
            'system_message': campaign.get('system_message'),
            'first_message': campaign.get('first_message'),
            'voice': campaign.get('voice'),
            'ai_provider': campaign.get('ai_provider'),
            'status': 'initiated'
        }
        
        logger.info(f"Campaign call initiated: {call_data}")
        
        # TODO: Integrate with your LiveKit dispatch system here
        # lk dispatch create --new-room --agent-name outbound-caller --metadata target_phone
        
        return jsonify({
            'message': 'Call initiated successfully',
            'call_data': call_data
        })
        
    except Exception as e:
        logger.error(f"Error starting campaign call: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/livekit/dispatch', methods=['POST'])
def dispatch_livekit_call():
    """Dispatch a LiveKit outbound call with specific SIP trunk"""
    try:
        data = request.json
        
        required_fields = ['target_phone', 'from_phone', 'sip_trunk_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        target_phone = data['target_phone']
        from_phone = data['from_phone']
        sip_trunk_id = data['sip_trunk_id']
        
        # Additional call parameters
        system_message = data.get('system_message', 'You are a helpful AI assistant.')
        voice = data.get('voice', 'Tara')
        agent_name = data.get('agent_name', 'outbound-caller')
        
        # Create call metadata
        call_metadata = {
            'target_phone': target_phone,
            'from_phone': from_phone,
            'sip_trunk_id': sip_trunk_id,
            'system_message': system_message,
            'voice': voice
        }
        
        # TODO: Here you would call your LiveKit CLI or use LiveKit SDK
        # Example command that would be executed:
        # lk dispatch create --new-room --agent-name {agent_name} --metadata '{json.dumps(call_metadata)}'
        
        logger.info(f"LiveKit dispatch requested: {call_metadata}")
        
        return jsonify({
            'message': 'LiveKit call dispatched successfully',
            'call_metadata': call_metadata,
            'status': 'dispatched'
        })
        
    except Exception as e:
        logger.error(f"Error dispatching LiveKit call: {str(e)}")
        return jsonify({'error': str(e)}), 500

def create_sip_trunk_config(phone_number, connection_id):
    """Create SIP trunk configuration for a phone number using your real Telnyx credentials"""
    
    # Create the SIP trunk configuration using your working credentials
    trunk_config = {
        "trunk": {
            "name": f"cbtest_{phone_number.replace('+', '').replace('-', '')}",
            "address": "itscb.sip.telnyx.com",
            "numbers": [phone_number],
            "auth_username": "itscb",
            "auth_password": "Iamcb123"
        }
    }
    
    return None, trunk_config  # We'll get the real SIP trunk ID from LiveKit response

def create_livekit_sip_trunk(phone_number, trunk_config):
    """Create SIP trunk in LiveKit using CLI and return the SIP trunk ID"""
    import tempfile
    import re
    
    try:
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(trunk_config, f, indent=2)
            temp_file = f.name
        
        try:
            # Run lk command (using environment credentials)
            cmd = ["lk", "sip", "outbound", "create", temp_file]
            logger.info(f"🚀 Creating SIP trunk for {phone_number}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)
            
            if result.returncode == 0:
                # Extract SIP trunk ID from output
                output = result.stdout.strip()
                logger.info(f"LiveKit output: {output}")
                
                # Try to extract SIP trunk ID
                sip_trunk_id = None
                try:
                    # Parse JSON output if possible
                    output_json = json.loads(output)
                    sip_trunk_id = output_json.get('sipTrunkId') or output_json.get('id')
                except json.JSONDecodeError:
                    # Try to extract from text output (like "SIPTrunkID: ST_...")
                    match = re.search(r'ST_[a-zA-Z0-9]+', output)
                    if match:
                        sip_trunk_id = match.group(0)
                
                if sip_trunk_id:
                    logger.info(f"✅ Successfully created SIP trunk {sip_trunk_id} for {phone_number}")
                    return sip_trunk_id
                else:
                    logger.error(f"Could not extract SIP trunk ID from output: {output}")
                    return None
            else:
                logger.error(f"Failed to create SIP trunk for {phone_number}: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error running lk command: {str(e)}")
            return None
            
    except Exception as e:
        logger.error(f"Error creating SIP trunk: {str(e)}")
        return None
    finally:
        # Clean up temp file
        try:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
        except:
            pass

def auto_create_sip_trunks_for_numbers(phone_numbers_data):
    """Automatically create SIP trunks for numbers that don't have them"""
    created_trunks = []
    
    for number_data in phone_numbers_data:
        phone_number = number_data.get('phone_number')
        connection_id = number_data.get('connection_id')
        
        if phone_number and phone_number not in sip_trunk_mapping:
            logger.info(f"🚀 Auto-creating SIP trunk for new number: {phone_number}")
            
            # Create SIP trunk configuration using your real Telnyx credentials
            _, trunk_config = create_sip_trunk_config(phone_number, connection_id)
            
            # Try to create the SIP trunk in LiveKit and get the real SIP trunk ID
            sip_trunk_id = create_livekit_sip_trunk(phone_number, trunk_config)
            
            if sip_trunk_id:
                # Store the mapping
                sip_trunk_mapping[phone_number] = sip_trunk_id
                # Save to persistent storage
                save_json_data(SIP_TRUNKS_FILE, sip_trunk_mapping)
                created_trunks.append({
                    'phone_number': phone_number,
                    'sip_trunk_id': sip_trunk_id,
                    'status': 'created'
                })
                logger.info(f"✅ Auto-created SIP trunk {sip_trunk_id} for {phone_number}")
            else:
                logger.error(f"❌ Failed to create SIP trunk for {phone_number}")
                created_trunks.append({
                    'phone_number': phone_number,
                    'sip_trunk_id': None,
                    'status': 'failed'
                })
    
    return created_trunks

def cleanup_removed_numbers(current_numbers):
    """Remove SIP trunk mappings for numbers that are no longer in Telnyx"""
    removed_numbers = []
    
    # Find numbers in mapping that are no longer in current_numbers
    current_phone_numbers = [num.get('phone_number') for num in current_numbers]
    
    for phone_number in list(sip_trunk_mapping.keys()):
        if phone_number not in current_phone_numbers:
            logger.info(f"🧹 Removing mapping for deleted number: {phone_number}")
            sip_trunk_id = sip_trunk_mapping.pop(phone_number)
            removed_numbers.append({
                'phone_number': phone_number,
                'sip_trunk_id': sip_trunk_id,
                'status': 'removed'
            })
    
    if removed_numbers:
        # Save updated mapping
        save_json_data(SIP_TRUNKS_FILE, sip_trunk_mapping)
        logger.info(f"✅ Cleaned up {len(removed_numbers)} removed numbers from mapping")
    
    return removed_numbers

# Call Logs and Contacts API
@app.route('/api/call-logs', methods=['GET'])
def get_call_logs():
    """Get all call logs with pagination and filtering"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        campaign_id = request.args.get('campaign_id')
        contact_phone = request.args.get('contact_phone')
        
        # Filter call logs
        filtered_logs = call_logs
        if campaign_id:
            filtered_logs = [log for log in filtered_logs if log.get('campaign_id') == campaign_id]
        if contact_phone:
            filtered_logs = [log for log in filtered_logs if log.get('contact_phone') == contact_phone]
        
        # Sort by timestamp (newest first)
        filtered_logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        # Paginate
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_logs = filtered_logs[start_idx:end_idx]
        
        return jsonify({
            'call_logs': paginated_logs,
            'total': len(filtered_logs),
            'page': page,
            'limit': limit,
            'has_more': end_idx < len(filtered_logs)
        })
        
    except Exception as e:
        logger.error(f"Error getting call logs: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/contacts', methods=['GET'])
def get_contacts():
    """Get unique contacts with call history"""
    try:
        # Group calls by contact phone number
        contacts_dict = {}
        
        for log in call_logs:
            phone = log.get('contact_phone', '')
            if phone:
                if phone not in contacts_dict:
                    contacts_dict[phone] = {
                        'phone_number': phone,
                        'name': log.get('contact_name', 'Unknown'),
                        'notes': log.get('notes', ''),
                        'total_calls': 0,
                        'successful_calls': 0,
                        'failed_calls': 0,
                        'last_called': None,
                        'campaigns': set()
                    }
                
                contact = contacts_dict[phone]
                contact['total_calls'] += 1
                
                if log.get('status') == 'initiated':
                    contact['successful_calls'] += 1
                elif log.get('status') == 'failed':
                    contact['failed_calls'] += 1
                
                # Update last called timestamp
                call_time = log.get('timestamp', '')
                if not contact['last_called'] or call_time > contact['last_called']:
                    contact['last_called'] = call_time
                    contact['name'] = log.get('contact_name', contact['name'])  # Update name from latest call
                
                # Track campaigns
                if log.get('campaign_id'):
                    contact['campaigns'].add(log.get('campaign_id'))
        
        # Convert to list and clean up sets
        contacts_list = []
        for contact in contacts_dict.values():
            contact['campaigns'] = list(contact['campaigns'])
            contacts_list.append(contact)
        
        # Sort by last called (newest first)
        contacts_list.sort(key=lambda x: x.get('last_called', ''), reverse=True)
        
        return jsonify({
            'contacts': contacts_list,
            'total': len(contacts_list)
        })
        
    except Exception as e:
        logger.error(f"Error getting contacts: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/contacts/<phone_number>/calls', methods=['GET'])
def get_contact_calls(phone_number):
    """Get call history for a specific contact"""
    try:
        # Find calls for this phone number
        contact_calls = [log for log in call_logs if log.get('contact_phone') == phone_number]
        
        # Sort by timestamp (newest first)
        contact_calls.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        return jsonify({
            'phone_number': phone_number,
            'calls': contact_calls,
            'total': len(contact_calls)
        })
        
    except Exception as e:
        logger.error(f"Error getting calls for contact {phone_number}: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Telnyx Recording API
@app.route('/api/recordings', methods=['GET'])
def get_recordings():
    """Get call recordings from Telnyx"""
    try:
        # Get recordings from Telnyx API
        recordings = telnyx.Recording.list()
        
        recording_list = []
        for recording in recordings.data:
            recording_list.append({
                'id': recording.id,
                'call_leg_id': recording.call_leg_id,
                'call_session_id': recording.call_session_id,
                'recording_started_at': recording.recording_started_at,
                'recording_ended_at': recording.recording_ended_at,
                'duration': recording.duration_millis,
                'file_size': recording.file_size,
                'download_url': recording.download_urls.get('mp3') if recording.download_urls else None,
                'status': recording.status
            })
        
        return jsonify({
            'recordings': recording_list,
            'total': len(recording_list)
        })
        
    except Exception as e:
        logger.error(f"Error getting recordings: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Campaign Statistics and Management Endpoints

@app.route('/api/campaigns/<campaign_id>/statistics', methods=['GET'])
def get_campaign_statistics_endpoint(campaign_id):
    """Get ACCURATE campaign statistics using direct database query"""
    try:
        # Get fresh campaign data from database
        fresh_campaigns = get_all_campaigns()
        campaign = fresh_campaigns.get(campaign_id)
        if not campaign:
            return jsonify({'error': 'Campaign not found'}), 404
        
        # Get all call logs for this campaign from database (same as enhanced endpoint)
        all_call_logs = get_all_call_logs()
        campaign_calls = [log for log in all_call_logs if log.get('campaign_id') == campaign_id]
        
        # Analyze calls using same logic as enhanced endpoint
        total_calls = len(campaign_calls)
        # SIMPLIFIED: Only two statuses - answered means success
        completed_calls = len([c for c in campaign_calls if c.get('status') == 'answered'])
        no_answer_calls = len([c for c in campaign_calls if c.get('status') not in ['answered']])
        failed_calls = 0  # We treat all non-answered as no_answer now
        short_calls = len([c for c in campaign_calls if c.get('call_duration', 0) < 10])
        
        # Duration analysis
        total_duration = sum(c.get('call_duration', 0) for c in campaign_calls)
        avg_duration = total_duration / total_calls if total_calls > 0 else 0
        
        # Cost analysis (estimate based on duration)
        cost_per_minute = 0.013  # Telnyx rate
        total_cost = (total_duration / 60) * cost_per_minute
        avg_cost_per_call = total_cost / total_calls if total_calls > 0 else 0
        
        # Format recent calls
        recent_calls = sorted(campaign_calls, key=lambda x: x.get('timestamp', ''), reverse=True)[:10]
        formatted_calls = []
        for call in recent_calls:
            formatted_calls.append({
                'id': call.get('id', ''),
                'contact_name': call.get('contact_name', 'Unknown'),
                'to_phone': call.get('contact_phone', ''),
                'from_phone': call.get('from_phone', ''),
                'status': call.get('status', 'unknown'),
                'start_time': call.get('timestamp', ''),
                'duration': call.get('call_duration', 0),
                'cost': (call.get('call_duration', 0) / 60) * cost_per_minute
            })
        
        # Build statistics response
            stats = {
                'totals': {
                'total_calls': total_calls,
                'completed_calls': completed_calls,
                'failed_calls': failed_calls,
                'no_answer_calls': no_answer_calls,
                'short_calls': short_calls,
                    'unknown_calls': 0
                },
                'duration': {
                'total_duration_seconds': total_duration,
                'total_duration_minutes': total_duration / 60,
                'average_duration_seconds': avg_duration
                },
                'costs': {
                'total_cost_usd': total_cost,
                'average_cost_per_call': avg_cost_per_call
            },
            'calls': formatted_calls
            }
        
        # Add campaign metadata and progress calculation
        total_contacts = len(campaign.get('contacts', []))
        total_calls_made = stats['totals']['total_calls']
        progress_percent = (total_calls_made / total_contacts * 100) if total_contacts > 0 else 0
        
        stats['campaign_info'] = {
            'id': campaign_id,
            'name': campaign.get('name', 'Unnamed Campaign'),
            'status': campaign.get('status', 'draft'),
            'created_at': campaign.get('created_at'),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'agent_name': campaign.get('agent_name'),
            'total_contacts': total_contacts
        }
        
        # Add progress percentage for frontend progress bar
        stats['progress_percent'] = round(progress_percent, 1)
        stats['call_statistics'] = {
            'total_calls': stats['totals']['total_calls'],
            'completed': stats['totals']['completed_calls'],
            'failed': stats['totals']['failed_calls'],
            'no_answer': stats['totals']['no_answer_calls']
        }
        
        logger.info(f"📊 FIXED statistics for campaign {campaign_id}: Total={stats['totals']['total_calls']}, Completed={stats['totals']['completed_calls']}, Failed={stats['totals']['failed_calls']}, No Answer={stats['totals']['no_answer_calls']}, Progress={progress_percent:.1f}%")
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Error getting campaign statistics: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/statistics/telnyx', methods=['GET'])
def get_campaign_statistics_telnyx(campaign_id):
    """Get TELNYX-VERIFIED campaign statistics using direct database query"""
    try:
        # Get fresh campaign data from database
        fresh_campaigns = get_all_campaigns()
        campaign = fresh_campaigns.get(campaign_id)
        if not campaign:
            return jsonify({'error': 'Campaign not found'}), 404
        
        # Get all call logs for this campaign from database (same approach as basic endpoint)
        all_call_logs = get_all_call_logs()
        campaign_calls = [log for log in all_call_logs if log.get('campaign_id') == campaign_id]
        
        # Analyze calls using same logic as basic endpoint
        total_calls = len(campaign_calls)
        # SIMPLIFIED: Only two statuses - answered means success
        completed_calls = len([c for c in campaign_calls if c.get('status') == 'answered'])
        no_answer_calls = len([c for c in campaign_calls if c.get('status') not in ['answered']])
        failed_calls = 0  # We treat all non-answered as no_answer now
        short_calls = len([c for c in campaign_calls if c.get('call_duration', 0) < 10])
        in_progress_calls = 0  # We don't track in-progress separately yet
        
        # Duration analysis
        total_duration = sum(c.get('call_duration', 0) for c in campaign_calls)
        avg_duration = total_duration / total_calls if total_calls > 0 else 0
        
        # Cost analysis (estimate based on duration)
        cost_per_minute = 0.013  # Telnyx rate
        total_cost = (total_duration / 60) * cost_per_minute
        avg_cost_per_call = total_cost / total_calls if total_calls > 0 else 0
        
        # Format recent calls
        recent_calls = sorted(campaign_calls, key=lambda x: x.get('timestamp', ''), reverse=True)[:10]
        formatted_calls = []
        for call in recent_calls:
            formatted_calls.append({
                'id': call.get('id', ''),
                'contact_name': call.get('contact_name', 'Unknown'),
                'to_phone': call.get('contact_phone', ''),
                'from_phone': call.get('from_phone', ''),
                'status': call.get('status', 'unknown'),
                'start_time': call.get('timestamp', ''),
                'duration': call.get('call_duration', 0),
                'cost': (call.get('call_duration', 0) / 60) * cost_per_minute,
                'telnyx_call_id': call.get('telnyx_call_id', '')
            })
        
        # Determine data source
        data_source = 'database_logs'
        if any(call.get('telnyx_call_id') for call in campaign_calls):
            data_source = 'telnyx_webhooks'
        
        # Build statistics response
            stats = {
                'totals': {
                'total_calls': total_calls,
                'completed_calls': completed_calls,
                'failed_calls': failed_calls,
                'no_answer_calls': no_answer_calls,
                'short_calls': short_calls,
                'in_progress_calls': in_progress_calls,
                    'unknown_calls': 0
                },
                'duration': {
                'total_duration_seconds': total_duration,
                'total_duration_minutes': total_duration / 60,
                'average_duration_seconds': avg_duration
                },
                'costs': {
                'total_cost_usd': total_cost,
                'average_cost_per_call': avg_cost_per_call
            },
            'calls': formatted_calls,
            'data_source': data_source
            }
        
        # Add campaign metadata and progress calculation
        total_contacts = len(campaign.get('contacts', []))
        total_calls_made = stats['totals']['total_calls']
        progress_percent = (total_calls_made / total_contacts * 100) if total_contacts > 0 else 0
        
        stats['campaign_info'] = {
            'id': campaign_id,
            'name': campaign.get('name', 'Unnamed Campaign'),
            'status': campaign.get('status', 'draft'),
            'created_at': campaign.get('created_at'),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'agent_name': campaign.get('agent_name'),
            'total_contacts': total_contacts
        }
        
        # Add progress percentage for frontend progress bar
        stats['progress_percent'] = round(progress_percent, 1)
        stats['call_statistics'] = {
            'total_calls': stats['totals']['total_calls'],
            'completed': stats['totals']['completed_calls'],
            'failed': stats['totals']['failed_calls'],
            'no_answer': stats['totals']['no_answer_calls']
        }
        
        logger.info(f"📊 TELNYX-VERIFIED statistics for campaign {campaign_id}: Data Source={stats['data_source']}, Total={stats['totals']['total_calls']}, Completed={stats['totals']['completed_calls']}, Failed={stats['totals']['failed_calls']}, No Answer={stats['totals']['no_answer_calls']}, Progress={progress_percent:.1f}%")
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Error getting Telnyx campaign statistics: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/statistics/enhanced', methods=['GET'])
def get_campaign_statistics_enhanced(campaign_id):
    """Get SIMPLE contact-focused campaign statistics"""
    try:
        # Get fresh campaign data from database
        fresh_campaigns = get_all_campaigns()
        campaign = fresh_campaigns.get(campaign_id)
        if not campaign:
            logger.warning(f"⚠️ Campaign {campaign_id} not found in database")
            return jsonify({'error': 'Campaign not found'}), 404
        
        # Get all call logs for this campaign from database
        all_call_logs = get_all_call_logs()
        campaign_calls = [log for log in all_call_logs if log.get('campaign_id') == campaign_id]
        
        # Basic campaign info
        campaign_info = {
            'id': campaign_id,
            'name': campaign.get('name', 'Unknown Campaign'),
            'status': campaign.get('status', 'unknown'),
            'created_at': campaign.get('created_at', ''),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'agent_name': campaign.get('agent_name', 'AI Agent'),
            'total_contacts': len(campaign.get('contacts', []))
        }
        
        # CONTACT-LEVEL ANALYSIS (what the user actually wants)
        # Group calls by phone number to get latest status per contact
        calls_by_contact = {}
        for call in campaign_calls:
            phone_number = call.get('contact_phone', '')
            if phone_number:
                # Keep the most recent call for each contact
                if phone_number not in calls_by_contact:
                    calls_by_contact[phone_number] = call
                else:
                    # Compare timestamps to keep the most recent
                    existing_time = calls_by_contact[phone_number].get('timestamp', '')
                    current_time = call.get('timestamp', '')
                    if current_time > existing_time:
                        calls_by_contact[phone_number] = call
        
        # Count contacts reached vs not reached
        contacts_reached = 0
        contacts_not_reached = 0
        
        for phone_number, latest_call in calls_by_contact.items():
            if latest_call.get('status') == 'answered':
                contacts_reached += 1
            else:
                contacts_not_reached += 1
        
        # Calculate contacts never called
        total_contacts = len(campaign.get('contacts', []))
        contacts_called = len(calls_by_contact)
        contacts_never_called = max(0, total_contacts - contacts_called)  # Prevent negative values
        
        # Adjust counts (contacts never called are also "not reached")
        contacts_not_reached += contacts_never_called
        
        # Ensure we don't have more contacts than the total
        actual_total_contacts = max(total_contacts, contacts_called)
        
        # Calculate estimated cost based on ANSWERED call durations only
        total_duration_seconds = 0
        total_cost_usd = 0.0
        total_answered_calls = 0
        
        # Only count costs for actual answered calls
        for call in campaign_calls:
            # Only calculate costs for answered calls
            if call.get('status') == 'answered':
                # Get call duration, only count if greater than zero
                duration = call.get('call_duration', 0)
                if duration > 0:
                    # Add to total duration
                    total_duration_seconds += duration
                    
                    # Calculate estimated cost based on duration
                    # Standard outbound rate of $0.013 per minute
                    estimated_cost = (duration / 60) * 0.013
                    
                    # Add to total cost
                    total_cost_usd += estimated_cost
                    total_answered_calls += 1
        
        # Include minimum connect fee for each answered call
        # Standard $0.005 connect fee per answered call
        connect_fee_per_call = 0.005
        connect_fees = total_answered_calls * connect_fee_per_call
        total_cost_usd += connect_fees
        
        # Build SIMPLE response focused on contacts
        enhanced_stats = {
            'success': True,
            'data': {
                'campaign_info': campaign_info,
                'contact_summary': {
                    'total_contacts': actual_total_contacts,
                    'contacts_reached': contacts_reached,
                    'contacts_not_reached': contacts_not_reached,
                    'contacts_called': contacts_called,
                    'contacts_never_called': contacts_never_called
                },
                'cost': {
                    'total_cost_usd': total_cost_usd,
                    'total_duration_seconds': total_duration_seconds,
                    'average_cost_per_call': total_cost_usd / max(1, len(campaign_calls)) if campaign_calls else total_cost_usd / max(1, actual_total_contacts)
                }
            }
        }
        
        logger.info(f"📊 SIMPLE contact statistics for campaign {campaign_id}: Total={actual_total_contacts}, Reached={contacts_reached}, Not Reached={contacts_not_reached}")
        
        return jsonify(enhanced_stats)
        
    except Exception as e:
        logger.error(f"Error getting enhanced campaign statistics: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to get enhanced campaign statistics',
            'message': str(e)
        }), 500

@app.route('/api/campaigns/<campaign_id>/unresponsive', methods=['GET'])
def get_campaign_unresponsive(campaign_id):
    """Get unresponsive contacts for a campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        # Get query parameters
        min_retry_interval_hours = float(request.args.get('min_retry_interval_hours', 1.0))
        
        if CAMPAIGN_STATS_AVAILABLE:
            unresponsive_contacts = get_unresponsive_contacts(campaign_id, str(DATA_DIR))
        else:
            # Enhanced fallback: get failed/no_answer calls from call logs using same logic as retry
            campaign_calls = [log for log in call_logs if log.get('campaign_id') == campaign_id]
            
            # Sort by timestamp to get most recent calls first (same as retry logic)
            campaign_calls.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            
            # Group calls by phone number to find the latest status for each number
            calls_by_number = {}
            for call in campaign_calls:
                phone_number = call.get('contact_phone', '')
                if phone_number and phone_number not in calls_by_number:
                    calls_by_number[phone_number] = call
            
            # Get numbers that are unresponsive (failed, no_answer, busy)
            failed_statuses = ['failed', 'no_answer', 'busy']
            unresponsive_contacts = []
            
            for phone_number, latest_call in calls_by_number.items():
                latest_status = latest_call.get('status', 'unknown')
                if latest_status in failed_statuses:
                    unresponsive_contacts.append({
                        'phone_number': phone_number,
                        'contact_name': latest_call.get('contact_name', ''),
                        'notes': latest_call.get('notes', ''),
                        'last_attempt': latest_call.get('timestamp', ''),
                        'reason': latest_status
                    })
        
        return jsonify({
            'campaign_id': campaign_id,
            'unresponsive_contacts': unresponsive_contacts,
            'total_unresponsive': len(unresponsive_contacts),
            'min_retry_interval_hours': min_retry_interval_hours
        })
        
    except Exception as e:
        logger.error(f"Error getting unresponsive contacts: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/recycle', methods=['POST'])
def recycle_campaign_unresponsive(campaign_id):
    """Recycle unresponsive contacts in a campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        request_data = request.get_json() or {}
        min_retry_interval = request_data.get('min_retry_interval_hours', 1)
        create_new_campaign = request_data.get('create_new_campaign', False)
        
        recycler = RecycleUnresponsive(str(DATA_DIR))
        
        if create_new_campaign:
            # Create a new campaign with unresponsive contacts
            result = recycler.create_recycled_campaign(
                campaign_id, campaigns_storage, min_retry_interval
            )
            
            if result['success']:
                # Save the new campaign
                save_json_data(CAMPAIGNS_FILE, campaigns_storage)
                
        else:
            # Recycle in place (restart with only unresponsive contacts)
            result = recycler.recycle_in_place(
                campaign_id, campaigns_storage, min_retry_interval
            )
            
            if result['success']:
                # Save updated campaign
                save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error recycling campaign: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/status', methods=['GET'])
def get_campaign_status(campaign_id):
    """Get real-time campaign status with call progress"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        
        # Get basic campaign info
        status_info = {
            'campaign_id': campaign_id,
            'name': campaign.get('name', 'Unnamed Campaign'),
            'status': campaign.get('status', 'draft'),
            'created_at': campaign.get('created_at'),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'total_contacts': len(campaign.get('contacts', []))
        }
        
        # Use accurate call log analyzer
        analyzer = CallLogAnalyzer(str(DATA_DIR))
        analysis = analyzer.analyze_campaign(campaign_id)
        
        if 'error' in analysis:
            # No call logs - return empty status
            status_counts = {
                'total_calls': 0,
                'completed': 0,
                'failed': 0,
                'no_answer': 0,
                'busy': 0,
                'in_progress': 0
            }
            total_duration = 0
            total_cost = 0.0
            campaign_calls = []
        else:
            # Use accurate analysis
            call_stats = analysis['call_statistics']
            status_counts = {
                'total_calls': call_stats['total_calls'],
                'completed': call_stats['completed'],
                'failed': call_stats['failed'],
                'no_answer': call_stats['no_answer'],
                'busy': 0,  # We don't track busy separately yet
                'in_progress': 0  # We don't track in-progress separately yet
            }
            
            total_duration = analysis['total_duration']
            total_cost = total_duration * 0.013 / 60  # ~$0.013 per minute
            campaign_calls = analysis['call_details']
        
        # Calculate progress percentage
        progress_percent = 0
        if status_info['total_contacts'] > 0:
            completed_calls = status_counts['completed'] + status_counts['failed'] + status_counts['no_answer']
            progress_percent = (completed_calls / status_info['total_contacts']) * 100
        
        # Combine all status information
        status_info.update({
            'call_statistics': status_counts,
            'progress_percent': round(progress_percent, 1),
            'total_duration_seconds': total_duration,
            'total_cost_estimate': round(total_cost, 4),
            'recent_calls': campaign_calls[-10:] if campaign_calls else []  # Last 10 calls
        })
        
        return jsonify(status_info)
        
    except Exception as e:
        logger.error(f"Error getting campaign status: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/active', methods=['GET'])
def get_active_campaigns():
    """Get all currently active campaigns"""
    try:
        active_campaigns = []
        
        for campaign_id, campaign in campaigns_storage.items():
            if campaign.get('status') in ['running', 'paused']:
                # Get quick stats for each active campaign
                campaign_calls = [log for log in call_logs if log.get('campaign_id') == campaign_id]
                
                active_campaigns.append({
                    'id': campaign_id,
                    'name': campaign.get('name', 'Unnamed Campaign'),
                    'status': campaign.get('status'),
                    'started_at': campaign.get('started_at'),
                    'total_contacts': len(campaign.get('contacts', [])),
                    'calls_made': len(campaign_calls),
                    'agent_name': campaign.get('agent_name')
                })
        
        return jsonify({
            'active_campaigns': active_campaigns,
            'total_active': len(active_campaigns)
        })
        
    except Exception as e:
        logger.error(f"Error getting active campaigns: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/debug/campaign/<campaign_id>', methods=['GET'])
def debug_campaign(campaign_id):
    """Debug endpoint to see what's happening with a campaign"""
    try:
        # Get campaign data
        campaign = campaigns_storage.get(campaign_id, {})
        
        # Get all call logs for this campaign
        campaign_calls = [log for log in call_logs if log.get('campaign_id') == campaign_id]
        
        # Get data directory contents for this campaign
        data_files = []
        try:
            import os
            data_dir = Path(__file__).parent / "data"
            if data_dir.exists():
                for file in data_dir.glob(f"*{campaign_id}*"):
                    data_files.append(str(file))
        except Exception as e:
            data_files = [f"Error reading data dir: {e}"]
        
        debug_info = {
            'campaign_id': campaign_id,
            'campaign_exists': campaign_id in campaigns_storage,
            'campaign_data': campaign,
            'call_logs_count': len(campaign_calls),
            'call_logs': campaign_calls,
            'data_files': data_files,
            'campaign_stats_available': CAMPAIGN_STATS_AVAILABLE,
            'all_campaigns': list(campaigns_storage.keys())
        }
        
        return jsonify(debug_info)
        
    except Exception as e:
        logger.error(f"Error in debug endpoint: {str(e)}")
        return jsonify({'error': str(e)}), 500

class EnhancedCallTracker:
    def __init__(self, data_dir: str = "data", telnyx_api_key: str = None):
        self.data_dir = Path(data_dir)
        self.telnyx_api_key = telnyx_api_key
        self.call_logs_file = self.data_dir / "enhanced_call_logs.json"
        self.campaigns_file = self.data_dir / "campaigns.json"
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # Initialize telnyx if API key available
        if self.telnyx_api_key:
            telnyx.api_key = self.telnyx_api_key
    
    def load_call_logs(self):
        """Load enhanced call logs"""
        if self.call_logs_file.exists():
            try:
                with open(self.call_logs_file, 'r') as f:
                    return json.load(f)
            except:
                return []
        return []
    
    def save_call_logs(self, logs):
        """Save enhanced call logs"""
        try:
            with open(self.call_logs_file, 'w') as f:
                json.dump(logs, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving call logs: {e}")
    
    def log_call_initiated(self, campaign_id: str, contact_name: str, contact_phone: str, 
                          from_phone: str, agent_name: str = None):
        """Log when a call is initiated"""
        logs = self.load_call_logs()
        
        call_id = f"call_{campaign_id}_{len(logs) + 1}_{int(time.time())}"
        
        call_log = {
            'id': call_id,
            'campaign_id': campaign_id,
            'contact_name': contact_name,
            'contact_phone': contact_phone,
            'from_phone': from_phone,
            'agent_name': agent_name,
            'status': 'initiated',
            'initiated_at': datetime.now().isoformat(),
            'duration_seconds': 0,
            'call_answered': False,
            'telnyx_call_id': None,
            'room_name': None,
            'final_status': 'pending',
            'needs_recycling': False
        }
        
        logs.append(call_log)
        self.save_call_logs(logs)
        
        logger.info(f"📞 Call initiated: {contact_name} ({contact_phone}) from {from_phone}")
        return call_id
    
    def update_call_with_room_info(self, call_id: str, room_name: str, telnyx_call_id: str = None):
        """Update call log with room and Telnyx information"""
        logs = self.load_call_logs()
        
        for call in logs:
            if call['id'] == call_id:
                call['room_name'] = room_name
                call['status'] = 'connected'
                call['connected_at'] = datetime.now().isoformat()
                if telnyx_call_id:
                    call['telnyx_call_id'] = telnyx_call_id
                break
        
        self.save_call_logs(logs)
        logger.info(f"📞 Call {call_id} connected to room {room_name}")
    
    def schedule_call_status_check(self, call_id: str, delay_seconds: int = 30):
        """Schedule a status check after call should have completed"""
        def check_status():
            self.check_and_update_call_status(call_id)
        
        Timer(delay_seconds, check_status).start()
        logger.info(f"⏰ Scheduled status check for call {call_id} in {delay_seconds} seconds")
    
    def check_and_update_call_status(self, call_id: str):
        """Check call status using Telnyx API and update accordingly"""
        logs = self.load_call_logs()
        call_log = None
        
        for call in logs:
            if call['id'] == call_id:
                call_log = call
                break
        
        if not call_log:
            logger.error(f"Call log not found for {call_id}")
            return
        
        # If already finalized, skip
        if call_log.get('final_status') != 'pending':
            return
        
        # Check if we have Telnyx call data
        telnyx_call_id = call_log.get('telnyx_call_id')
        room_name = call_log.get('room_name')
        
        # Determine call outcome based on available data
        call_duration = 0
        call_answered = False
        final_status = 'no_answer'
        
        if self.telnyx_api_key and telnyx_call_id:
            # Try to get real Telnyx call data
            try:
                telnyx_data = self.get_telnyx_call_data(telnyx_call_id)
                if telnyx_data and 'duration' in telnyx_data:
                    call_duration = telnyx_data['duration']
                    call_answered = call_duration > 0
                    
                    if call_duration >= 10:  # Call lasted 10+ seconds
                        final_status = 'completed'
                    elif call_duration > 0:
                        final_status = 'short_call'
                    else:
                        final_status = 'no_answer'
                        
            except Exception as e:
                logger.warning(f"Could not get Telnyx data for call {call_id}: {e}")
        
        # Update call log
        call_log['duration_seconds'] = call_duration
        call_log['call_answered'] = call_answered
        call_log['final_status'] = final_status
        call_log['status'] = 'completed'
        call_log['completed_at'] = datetime.now().isoformat()
        call_log['needs_recycling'] = (final_status in ['no_answer', 'failed'])
        
        self.save_call_logs(logs)
        
        logger.info(f"📊 Call {call_id} finalized: {final_status} (Duration: {call_duration}s)")
        
        return final_status
    
    def get_telnyx_call_data(self, call_id: str):
        """Get call data from Telnyx API"""
        try:
            headers = {
                'Authorization': f'Bearer {self.telnyx_api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"https://api.telnyx.com/v2/calls/{call_id}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('data', {})
            else:
                logger.warning(f"Telnyx API returned {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching Telnyx call data: {e}")
            return None
    
    def get_campaign_statistics(self, campaign_id: str):
        """Get comprehensive campaign statistics"""
        logs = self.load_call_logs()
        campaign_calls = [call for call in logs if call['campaign_id'] == campaign_id]
        
        if not campaign_calls:
            return {
                'campaign_id': campaign_id,
                'total_calls': 0,
                'completed': 0,
                'short_calls': 0,
                'no_answer': 0,
                'failed': 0,
                'in_progress': 0,
                'total_duration': 0,
                'needs_recycling': []
            }
        
        stats = {
            'completed': 0,
            'short_calls': 0,
            'no_answer': 0,
            'failed': 0,
            'in_progress': 0
        }
        
        total_duration = 0
        needs_recycling = []
        
        for call in campaign_calls:
            final_status = call.get('final_status', 'pending')
            duration = call.get('duration_seconds', 0)
            
            if final_status == 'pending':
                stats['in_progress'] += 1
            elif final_status in stats:
                stats[final_status] += 1
            
            total_duration += duration
            
            if call.get('needs_recycling', False):
                needs_recycling.append({
                    'contact_name': call['contact_name'],
                    'contact_phone': call['contact_phone'],
                    'reason': final_status
                })
        
        return {
            'campaign_id': campaign_id,
            'total_calls': len(campaign_calls),
            'completed': stats['completed'],
            'short_calls': stats['short_calls'], 
            'no_answer': stats['no_answer'],
            'failed': stats['failed'],
            'in_progress': stats['in_progress'],
            'total_duration': total_duration,
            'needs_recycling': needs_recycling,
            'call_details': campaign_calls
        }
    
    def get_numbers_for_recycling(self, campaign_id: str):
        """Get phone numbers that need to be recycled (didn't answer/failed)"""
        logs = self.load_call_logs()
        campaign_calls = [call for call in logs if call['campaign_id'] == campaign_id]
        
        recycling_numbers = []
        for call in campaign_calls:
            if call.get('needs_recycling', False):
                recycling_numbers.append({
                    'name': call['contact_name'],
                    'phone_number': call['contact_phone'],
                    'reason': call.get('final_status', 'unknown'),
                    'last_attempt': call.get('completed_at', call.get('initiated_at'))
                })
        
        return recycling_numbers

# Create global call tracker instance
enhanced_call_tracker = EnhancedCallTracker(str(DATA_DIR), TELNYX_API_KEY)

# Enhanced campaign monitoring and statistics
@app.route('/api/campaigns/<campaign_id>/monitor', methods=['GET'])
def monitor_campaign(campaign_id):
    """Get real-time campaign monitoring data"""
    try:
        # Get fresh campaign data from database
        fresh_campaigns = get_all_campaigns()
        if campaign_id not in fresh_campaigns:
            logger.warning(f"⚠️ Campaign {campaign_id} not found in database")
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = fresh_campaigns[campaign_id]
        
        # Get call logs for this campaign from database
        all_call_logs = get_all_call_logs()
        campaign_call_logs = [
            log for log in all_call_logs 
            if log.get('campaign_id') == campaign_id
        ]
        
        # Calculate real-time statistics with proper status tracking
        total_contacts = len(campaign.get('contacts', []))
        initiated_calls = len([log for log in campaign_call_logs if log.get('status') == 'initiated'])
        answered_calls = len([log for log in campaign_call_logs if log.get('status') == 'answered'])
        no_answer_calls = len([log for log in campaign_call_logs if log.get('status') == 'no_answer'])
        failed_calls = len([log for log in campaign_call_logs if log.get('status') == 'failed'])
        
        # Get campaign results if available
        call_results = campaign.get('call_results', {})
        
        monitoring_data = {
            'campaign_id': campaign_id,
            'name': campaign.get('name', 'Unnamed Campaign'),
            'status': campaign.get('status', 'unknown'),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'total_duration': campaign.get('total_duration'),
            'contacts': {
                'total': total_contacts,
                'called': len(campaign_call_logs),
                'initiated': initiated_calls,
                'answered': answered_calls,
                'no_answer': no_answer_calls,
                'failed': failed_calls,
                'remaining': max(0, total_contacts - len(campaign_call_logs))
            },
            'answer_rate': round((answered_calls / max(1, len(campaign_call_logs))) * 100, 1) if campaign_call_logs else 0,
            'success_rate': round((answered_calls / max(1, len(campaign_call_logs))) * 100, 1) if campaign_call_logs else 0,
            'phone_numbers': campaign.get('telnyx_numbers', []),
            'agent': {
                'id': campaign.get('agent_id'),
                'name': campaign.get('agent_name')
            },
            'call_results': call_results,
            'recent_calls': campaign_call_logs[-10:] if campaign_call_logs else [],  # Last 10 calls
            'errors': call_results.get('errors', []) if call_results else []
        }
        
        return jsonify(monitoring_data)
        
    except Exception as e:
        logger.error(f"Error monitoring campaign {campaign_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/performance', methods=['GET'])
def get_campaigns_performance():
    """Get performance statistics for all campaigns"""
    try:
        performance_data = []
        
        for campaign_id, campaign in campaigns_storage.items():
            # Get call logs for this campaign
            campaign_call_logs = [
                log for log in call_logs 
                if log.get('campaign_id') == campaign_id
            ]
            
            total_contacts = len(campaign.get('contacts', []))
            initiated_calls = len([log for log in campaign_call_logs if log.get('status') == 'initiated'])
            answered_calls = len([log for log in campaign_call_logs if log.get('status') == 'answered'])
            no_answer_calls = len([log for log in campaign_call_logs if log.get('status') == 'no_answer'])
            failed_calls = len([log for log in campaign_call_logs if log.get('status') == 'failed'])
            
            # Calculate duration
            duration = None
            if campaign.get('started_at') and campaign.get('completed_at'):
                try:
                    start_time = datetime.fromisoformat(campaign.get('started_at'))
                    end_time = datetime.fromisoformat(campaign.get('completed_at'))
                    duration = (end_time - start_time).total_seconds()
                except:
                    duration = campaign.get('total_duration')
            
            performance_data.append({
                'campaign_id': campaign_id,
                'name': campaign.get('name', 'Unnamed'),
                'status': campaign.get('status', 'unknown'),
                'created_at': campaign.get('created_at'),
                'started_at': campaign.get('started_at'),
                'completed_at': campaign.get('completed_at'),
                'duration_seconds': duration,
                'contacts_total': total_contacts,
                'calls_attempted': len(campaign_call_logs),
                'calls_initiated': initiated_calls,
                'calls_answered': answered_calls,
                'calls_no_answer': no_answer_calls,
                'calls_failed': failed_calls,
                'answer_rate': round((answered_calls / max(1, len(campaign_call_logs))) * 100, 1) if campaign_call_logs else 0,
                'success_rate': round((answered_calls / max(1, len(campaign_call_logs))) * 100, 1) if campaign_call_logs else 0,
                'phone_numbers_count': len(campaign.get('telnyx_numbers', [])),
                'agent_name': campaign.get('agent_name'),
                'has_errors': bool(campaign.get('call_results', {}).get('errors'))
            })
        
        # Sort by created_at (most recent first)
        performance_data.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        
        # Calculate overall statistics
        total_campaigns = len(performance_data)
        completed_campaigns = len([c for c in performance_data if c['status'] == 'completed'])
        running_campaigns = len([c for c in performance_data if c['status'] == 'running'])
        failed_campaigns = len([c for c in performance_data if c['status'] == 'failed'])
        
        total_calls = sum(c['calls_attempted'] for c in performance_data)
        total_answered = sum(c['calls_answered'] for c in performance_data)
        total_initiated = sum(c['calls_initiated'] for c in performance_data)
        
        return jsonify({
            'summary': {
                'total_campaigns': total_campaigns,
                'completed': completed_campaigns,
                'running': running_campaigns,
                'failed': failed_campaigns,
                'total_calls': total_calls,
                'total_initiated': total_initiated,
                'total_answered': total_answered,
                'overall_answer_rate': round((total_answered / max(1, total_calls)) * 100, 1),
                'overall_success_rate': round(((total_answered + total_initiated) / max(1, total_calls)) * 100, 1)
            },
            'campaigns': performance_data
        })
        
    except Exception as e:
        logger.error(f"Error getting campaigns performance: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Campaign pause/resume functionality for better control
@app.route('/api/campaigns/<campaign_id>/pause', methods=['POST'])
def pause_campaign(campaign_id):
    """Pause a running campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        
        if campaign.get('status') != 'running':
            return jsonify({'error': 'Campaign is not running'}), 400
        
        campaign['status'] = 'paused'
        campaign['paused_at'] = datetime.now().isoformat()
        
        save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        logger.info(f"⏸️ Campaign {campaign_id} paused")
        
        return jsonify({
            'message': 'Campaign paused successfully',
            'campaign': {
                'id': campaign_id,
                'status': campaign['status'],
                'paused_at': campaign['paused_at']
            }
        })
        
    except Exception as e:
        logger.error(f"Error pausing campaign {campaign_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/resume', methods=['POST'])
def resume_campaign(campaign_id):
    """Resume a paused campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        
        if campaign.get('status') != 'paused':
            return jsonify({'error': 'Campaign is not paused'}), 400
        
        campaign['status'] = 'running'
        campaign['resumed_at'] = datetime.now().isoformat()
        
        save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        logger.info(f"▶️ Campaign {campaign_id} resumed")
        
        return jsonify({
            'message': 'Campaign resumed successfully',
            'campaign': {
                'id': campaign_id,
                'status': campaign['status'],
                'resumed_at': campaign['resumed_at']
            }
        })
        
    except Exception as e:
        logger.error(f"Error resuming campaign {campaign_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/system/concurrent-calling/health', methods=['GET'])
def check_concurrent_calling_health():
    """Health check for concurrent calling system"""
    try:
        # Check active campaigns
        running_campaigns = [
            cid for cid, campaign in campaigns_storage.items() 
            if campaign.get('status') == 'running'
        ]
        
        # Check available phone numbers
        available_phones = []
        for campaign_id, campaign in campaigns_storage.items():
            if campaign.get('telnyx_numbers'):
                available_phones.extend(campaign.get('telnyx_numbers', []))
        
        unique_phones = list(set(available_phones))
        
        # Check SIP trunk mappings
        sip_trunk_count = len(sip_trunk_mapping)
        
        # Check agent availability
        available_agents = len(agents_storage)
        
        # Check recent call success rate
        recent_calls = call_logs[-100:] if len(call_logs) > 100 else call_logs
        recent_successful = len([log for log in recent_calls if log.get('status') == 'initiated'])
        recent_success_rate = (recent_successful / max(1, len(recent_calls))) * 100 if recent_calls else 100
        
        # Determine overall health
        health_status = 'healthy'
        issues = []
        
        if len(running_campaigns) > 5:
            health_status = 'warning'
            issues.append(f'High number of running campaigns: {len(running_campaigns)}')
        
        if len(unique_phones) < 2:
            health_status = 'warning'  
            issues.append(f'Limited phone numbers available: {len(unique_phones)}')
        
        if recent_success_rate < 70:
            health_status = 'warning'
            issues.append(f'Low recent success rate: {recent_success_rate:.1f}%')
        
        if available_agents == 0:
            health_status = 'critical'
            issues.append('No agents available')
        
        if len(running_campaigns) > 10:
            health_status = 'critical'
            issues.append(f'Too many concurrent campaigns: {len(running_campaigns)}')
        
        health_data = {
            'status': health_status,
            'timestamp': datetime.now().isoformat(),
            'concurrent_calling': {
                'running_campaigns': len(running_campaigns),
                'available_phone_numbers': len(unique_phones),
                'sip_trunk_mappings': sip_trunk_count,
                'available_agents': available_agents,
                'recent_success_rate': round(recent_success_rate, 1),
                'recent_calls_analyzed': len(recent_calls)
            },
            'recommendations': {
                'max_concurrent_campaigns': 5,
                'recommended_phone_numbers': max(2, len(running_campaigns)),
                'optimal_success_rate': 85
            },
            'issues': issues,
            'active_campaigns': [
                {
                    'id': cid,
                    'name': campaigns_storage[cid].get('name', 'Unnamed'),
                    'started_at': campaigns_storage[cid].get('started_at'),
                    'contacts_count': len(campaigns_storage[cid].get('contacts', []))
                }
                for cid in running_campaigns
            ],
            'phone_numbers': unique_phones
        }
        
        return jsonify(health_data)
        
    except Exception as e:
        logger.error(f"Error checking concurrent calling health: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

# Transfer functionality removed - the old working version didn't use immediate transfers
# The LiveKit system handles call routing automatically

# Transfer function from old working version
def transfer_call_to_target(call_control_id: str, from_number: str, to_number: str, contact_name: str) -> bool:
    """Transfer a Telnyx call to the target number"""
    try:
        # Get Telnyx API key
        telnyx_api_key = os.getenv('TELNYX_API_KEY')
        if not telnyx_api_key:
            logger.error("❌ TELNYX_API_KEY not found in environment variables")
            return False
        
        # Prepare transfer request
        transfer_url = f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/transfer"
        headers = {
            "Authorization": f"Bearer {telnyx_api_key}",
            "Content-Type": "application/json"
        }
        
        transfer_data = {
            "to": to_number,
            "from": from_number,
            "command_id": f"direct_{call_control_id}"
        }
        
        logger.info(f"📞 🔄 Transferring call {call_control_id} to {to_number} for {contact_name}")
        logger.info(f"📞 Transfer URL: {transfer_url}")
        logger.info(f"📞 Transfer data: {json.dumps(transfer_data, indent=2)}")
        
        # Make the transfer request
        response = requests.post(transfer_url, headers=headers, json=transfer_data)  # REMOVED 10-second timeout
        
        if response.status_code == 200:
            logger.info(f"📞 ✅ Transfer successful for {contact_name}: {response.status_code}")
            logger.info(f"📞 Transfer response: {response.text}")
            return True
        else:
            logger.error(f"📞 ❌ Transfer failed for {contact_name}: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"📞 ❌ Exception during call transfer for {contact_name}: {str(e)}")
        return False

# Telnyx webhook handler for real-time call status updates (WORKING VERSION FROM OLD FILE)
@app.route('/api/webhooks/telnyx', methods=['POST'])
@app.route('/api/telnyx/webhook', methods=['POST'])  # Alternative endpoint for Telnyx webhook
def handle_telnyx_webhook():
    """Handle Telnyx webhook events for real-time call status tracking"""
    try:
        webhook_data = request.json
        logger.info(f"📞 Telnyx webhook received: {json.dumps(webhook_data, indent=2)}")
        
        if not webhook_data or 'data' not in webhook_data:
            logger.warning("⚠️ Invalid webhook data received")
            return jsonify({'status': 'error', 'message': 'Invalid webhook data'}), 400
        
        event_data = webhook_data['data']
        event_type = event_data.get('event_type')
        payload = event_data.get('payload', {})
        
        call_control_id = payload.get('call_control_id')
        from_number = payload.get('from')
        to_number = payload.get('to')
        call_session_id = payload.get('call_session_id')
        
        logger.info(f"📞 Processing event: {event_type} for call {call_control_id}")
        logger.info(f"📞 From: {from_number} -> To: {to_number}")
        
        # CRITICAL FIX: Get latest call logs from database
        all_call_logs = get_all_call_logs()
        logger.debug(f"📞 Loaded {len(all_call_logs)} call logs from database")
        
        # Find the corresponding call log entry
        call_log_entry = None
        call_log_index = -1
        
        # Special handling for call.cost events
        if event_type == 'call.cost' and call_control_id:
            # For cost events, find the call by telnyx_call_id
            for i, log in enumerate(all_call_logs):
                if log.get('telnyx_call_id') == call_control_id:
                    call_log_entry = log
                    call_log_index = i
                    break
        else:
            # For other events, use the standard matching
            for i, log in enumerate(all_call_logs):
                # Primary match: target phone number matches
                if log.get('contact_phone') == to_number:
                    # Check if it's a recent call (within last hour)
                    try:
                        log_time = datetime.fromisoformat(log.get('timestamp', ''))
                        time_diff = datetime.now() - log_time
                        if time_diff.total_seconds() < 3600:  # Within 1 hour
                            # For campaigns: also check from_phone if available
                            # For single calls: from_phone might be empty, so just match on target number + time
                            log_from_phone = log.get('from_phone', '')
                            
                            # Match if:
                            # 1. Single call (empty campaign_id) and target matches and recent, OR
                            # 2. Campaign call and both phones match, OR  
                            # 3. Any recent call to target number (fallback for webhook number mismatches)
                            if (not log.get('campaign_id') or  # Single call
                                log_from_phone == from_number or  # Exact from_phone match  
                                not log_from_phone):  # Empty from_phone (fallback)
                                call_log_entry = log
                                call_log_index = i
                                logger.info(f"📞 ✅ Matched call log: {log.get('contact_name', 'Unknown')} - Type: {'Single' if not log.get('campaign_id') else 'Campaign'}")
                                break
                    except (ValueError, TypeError):
                        # Skip logs with invalid timestamps
                        continue
        
        if not call_log_entry:
            if event_type == 'call.cost':
                # For cost events, directly update the call in database if we can find it
                try:
                    total_cost = payload.get('total_cost', '0.0000')
                    billed_duration = payload.get('billed_duration_secs', 0)
                    
                    with qc_db.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("""
                            UPDATE call_logs 
                            SET updated_at = ?
                            WHERE telnyx_call_id = ?
                        """, (
                            datetime.now().isoformat(),
                            call_control_id
                        ))
                        rows_updated = cursor.rowcount
                        conn.commit()
                        
                        if rows_updated > 0:
                            logger.info(f"📋 Updated call cost directly in database: ${total_cost}")
                            return jsonify({'status': 'success', 'message': 'Cost updated in database'})
                except Exception as e:
                    logger.error(f"❌ Error updating cost info directly: {e}")
            
            logger.warning(f"⚠️ No matching call log found for {from_number} -> {to_number}")
            # Still process the webhook for logging purposes
            return jsonify({'status': 'received', 'message': 'No matching call log found'})
        
        # Update call log based on event type
        original_status = call_log_entry.get('status', 'unknown')
        updated = False
        
        # SIMPLIFIED: Only two states - "answered" or "no_answer"
        if event_type == 'call.initiated':
            logger.info(f"📞 Call initiated: {call_log_entry.get('contact_name', 'Unknown')}")
            # Update the call log in database with Telnyx call ID for first time
            if call_control_id and call_log_entry:
                # Find by call log ID and update with Telnyx info
                try:
                    with qc_db.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("""
                            UPDATE call_logs 
                            SET telnyx_call_id = ?, status = 'initiated', updated_at = ?
                            WHERE id = ?
                        """, (call_control_id, datetime.now().isoformat(), call_log_entry.get('id')))
                        conn.commit()
                        logger.info(f"✅ Updated call log with Telnyx ID {call_control_id}")
                except Exception as e:
                    logger.error(f"❌ Error updating call log with Telnyx ID: {e}")
            updated = True
            
            # Auto-transfer call
            try:
                transfer_success = transfer_call_to_target(call_control_id, from_number, to_number, call_log_entry.get('contact_name', 'Unknown'))
                if transfer_success:
                    logger.info(f"📞 ✅ Call transfer initiated for {call_log_entry.get('contact_name', 'Unknown')}")
                    # Update transfer status in database
                    try:
                        with qc_db.get_connection() as conn:
                            cursor = conn.cursor()
                            cursor.execute("""
                                UPDATE call_logs 
                                SET notes = COALESCE(notes, '') || ' Transfer: initiated'
                                WHERE id = ?
                            """, (call_log_entry.get('id'),))
                            conn.commit()
                    except Exception as e:
                        logger.error(f"❌ Error updating transfer status: {e}")
                else:
                    logger.error(f"📞 ❌ Call transfer failed for {call_log_entry.get('contact_name', 'Unknown')}")
                    # Update transfer status in database
                    try:
                        with qc_db.get_connection() as conn:
                            cursor = conn.cursor()
                            cursor.execute("""
                                UPDATE call_logs 
                                SET notes = COALESCE(notes, '') || ' Transfer: failed'
                                WHERE id = ?
                            """, (call_log_entry.get('id'),))
                            conn.commit()
                    except Exception as e:
                        logger.error(f"❌ Error updating transfer status: {e}")
            except Exception as e:
                logger.error(f"📞 ❌ Error transferring call for {call_log_entry.get('contact_name', 'Unknown')}: {str(e)}")
                # Update transfer status in database
                try:
                    with qc_db.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("""
                            UPDATE call_logs 
                            SET notes = COALESCE(notes, '') || ' Transfer: error'
                            WHERE id = ?
                        """, (call_log_entry.get('id'),))
                        conn.commit()
                except Exception as e:
                    logger.error(f"❌ Error updating transfer status: {e}")
            
        elif event_type == 'call.answered':
            logger.info(f"📞 ✅ CALL ANSWERED: {call_log_entry.get('contact_name', 'Unknown')} ({to_number})")
            # SIMPLE: Telnyx says answered = SUCCESS! No duration needed.
            call_log_entry['status'] = 'answered'
            call_log_entry['answered_at'] = datetime.now().isoformat()
            
            # Update in database directly by specific record ID
            try:
                with qc_db.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        UPDATE call_logs 
                        SET status = 'answered', answered_at = ?, updated_at = ?
                        WHERE id = ?
                    """, (
                        datetime.now().isoformat(), 
                        datetime.now().isoformat(),
                        call_log_entry.get('id')
                    ))
                    conn.commit()
                    logger.info(f"✅ MARKED AS ANSWERED: {call_log_entry.get('contact_name', 'Unknown')}")
            except Exception as e:
                logger.error(f"❌ Error updating answered status: {e}")
            updated = True
            
        elif event_type == 'call.hangup':
            # Handle call completion with sophisticated status detection
            duration = payload.get('duration', 0)  # Note: it's 'duration' not 'duration_seconds'
            hangup_cause = payload.get('hangup_cause', '')
            current_status = call_log_entry.get('status', 'initiated')
            
            # Check for conversation indicators (packet count > 0 means conversation happened)
            call_quality_stats = payload.get('call_quality_stats', {})
            inbound_stats = call_quality_stats.get('inbound', {})
            packet_count = int(inbound_stats.get('packet_count', 0))
            
            # SOPHISTICATED DETECTION: If there was packet exchange, the call was answered
            if current_status != 'answered' and packet_count > 0:
                logger.info(f"📞 ✅ CALL ANSWERED (detected from conversation): {call_log_entry.get('contact_name', 'Unknown')} - Packets: {packet_count}")
                current_status = 'answered'
                
                # Update status to answered in database
                try:
                    with qc_db.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("""
                            UPDATE call_logs 
                            SET status = 'answered', answered_at = ?, call_duration = ?, ended_at = ?, updated_at = ?
                            WHERE id = ?
                        """, (
                            datetime.now().isoformat(),
                            duration,
                            datetime.now().isoformat(),
                            datetime.now().isoformat(),
                            call_log_entry.get('id')
                        ))
                        conn.commit()
                        logger.info(f"✅ MARKED AS ANSWERED: {call_log_entry.get('contact_name', 'Unknown')} (Packets: {packet_count})")
                except Exception as e:
                    logger.error(f"❌ Error updating answered status: {e}")
            else:
                # Just update duration and end time, keep existing status
                try:
                    with qc_db.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("""
                            UPDATE call_logs 
                            SET call_duration = ?, ended_at = ?, updated_at = ?
                            WHERE id = ?
                        """, (
                            duration,
                            datetime.now().isoformat(),
                            datetime.now().isoformat(),
                            call_log_entry.get('id')
                        ))
                        conn.commit()
                        logger.info(f"📋 Updated call duration: {duration}s")
                except Exception as e:
                    logger.error(f"❌ Error updating hangup info: {e}")
            
            logger.info(f"📞 📋 Call ended: {call_log_entry.get('contact_name', 'Unknown')} - Duration: {duration}s - Status: {current_status} - Packets: {packet_count}")
            updated = True
            
        elif event_type == 'call.bridged':
            logger.info(f"📞 🌉 Call bridged: {call_log_entry.get('contact_name', 'Unknown')} - Connected to LiveKit")
            # Just log - wait for call.answered event to mark as successful
            updated = False  # No status change needed
            
        elif event_type in ['call.speak.ended', 'call.speak.started']:
            logger.info(f"📞 🗣️ TTS event: {event_type} for {call_log_entry.get('contact_name', 'Unknown')}")
            # Don't change status for TTS events, just log
            
        elif event_type == 'call.cost':
            # Handle call cost information
            total_cost = payload.get('total_cost', '0.0000')
            billed_duration = payload.get('billed_duration_secs', 0)
            cost_parts = payload.get('cost_parts', [])
            
            logger.info(f"📞 💰 Cost event: Total=${total_cost} for {billed_duration}s")
            
            # Update call log with cost information
            try:
                with qc_db.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        UPDATE call_logs 
                        SET updated_at = ?
                        WHERE telnyx_call_id = ?
                    """, (
                        datetime.now().isoformat(),
                        call_control_id
                    ))
                    conn.commit()
                    logger.info(f"📋 Updated call cost: ${total_cost}")
            except Exception as e:
                logger.error(f"❌ Error updating cost info: {e}")
            updated = True
            
        else:
            logger.info(f"📞 Other event: {event_type} for {call_log_entry.get('contact_name', 'Unknown')}")
        
        # Database operations are atomic - no need for complex save logic
        if updated and call_log_entry:
            logger.info(f"📋 Database updated for {call_log_entry.get('contact_name', 'Unknown')} - Event: {event_type}")
        else:
            logger.debug(f"📋 No database update needed for {event_type}")
        
        return jsonify({
            'status': 'success',
            'message': f'Webhook processed: {event_type}',
            'call_control_id': call_control_id,
            'updated': updated
        })
        
    except Exception as e:
        logger.error(f"❌ Error processing Telnyx webhook: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/webhooks/telnyx/test', methods=['POST', 'GET'])
def test_telnyx_webhook():
    """Test endpoint for Telnyx webhook configuration"""
    if request.method == 'GET':
        return jsonify({
            'status': 'ready',
            'message': 'Telnyx webhook endpoint is ready',
            'timestamp': datetime.now().isoformat(),
            'endpoint': '/api/webhooks/telnyx'
        })
    
    # Handle test webhook
    return jsonify({
        'status': 'test_received',
        'data': request.json,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/campaigns/<campaign_id>/restart', methods=['POST'])
def restart_campaign(campaign_id):
    """Restart a campaign by creating a new campaign with the same configuration"""
    try:
        # Get fresh campaign data from database
        fresh_campaigns = get_all_campaigns()
        original_campaign = fresh_campaigns.get(campaign_id)
        if not original_campaign:
            return jsonify({'error': 'Campaign not found'}), 404
        
        # Create a new campaign with the same configuration but fresh state
        new_campaign_id = f"campaign_{int(time.time())}"
        
        # Copy the original campaign configuration
        new_campaign = {
            'id': new_campaign_id,
            'name': f"{original_campaign.get('name', 'Unnamed')} (Restart)",
            'status': 'draft',
            'telnyxNumbers': original_campaign.get('telnyxNumbers', []),
            'agent_id': original_campaign.get('agent_id', ''),
            'agent_name': original_campaign.get('agent_name', ''),
            'agent_voice': original_campaign.get('agent_voice', ''),
            'agent_model': original_campaign.get('agent_model', ''),
            'agent_system_prompt': original_campaign.get('agent_system_prompt', ''),
            'agent_emotional_prompt': original_campaign.get('agent_emotional_prompt', ''),
            'first_message': original_campaign.get('first_message', ''),
            'contacts': original_campaign.get('contacts', []),
            'created_at': datetime.now().isoformat(),
            'restart_from': campaign_id,
            'restart_at': datetime.now().isoformat()
        }
        
        # Add the new campaign to storage
        fresh_campaigns[new_campaign_id] = new_campaign
        
        # Save to database
        save_campaigns_to_db(fresh_campaigns)
        
        logger.info(f"🔄 Restarted campaign {campaign_id} as new campaign {new_campaign_id}")
        
        return jsonify({
            'success': True,
            'message': f'Campaign restarted successfully',
            'original_campaign_id': campaign_id,
            'new_campaign_id': new_campaign_id,
            'new_campaign_name': new_campaign['name'],
            'contacts_count': len(new_campaign['contacts'])
        })
        
    except Exception as e:
        logger.error(f"❌ Error restarting campaign: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': 'Failed to restart campaign',
            'message': str(e)
        }), 500

@app.route('/api/campaigns/<campaign_id>/retry-unanswered', methods=['POST'])
def retry_unanswered_calls(campaign_id):
    """Retry unanswered/failed calls using sophisticated recycling logic from CALLBOT2.1"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        # Get campaign data
        campaign = campaigns_storage[campaign_id]
        
        logger.info(f"🔄 Processing retry request for campaign {campaign_id}: {campaign.get('name', 'Unknown')}")
        
        # Get all call logs for this campaign from database (most recent first)
        all_call_logs = get_all_call_logs()
        campaign_calls = [log for log in all_call_logs if log.get('campaign_id') == campaign_id]
        
        if not campaign_calls:
            return jsonify({
                'success': True,
                'message': 'No calls found for this campaign yet. Start the campaign first.',
                'retry_count': 0,
                'campaign_restarted': False
            })
        
        # Sort by timestamp to get most recent calls first
        campaign_calls.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        # Group calls by phone number to find the latest status for each number
        # This follows the exact logic from CALLBOT2.1's recycleCampaign function
        calls_by_number = {}
        for call in campaign_calls:
            phone_number = call.get('contact_phone', '')
            if phone_number and phone_number not in calls_by_number:
                # Only track this call if we haven't seen this number before (since we ordered by most recent)
                calls_by_number[phone_number] = call
        
        # SIMPLE: Only 'answered' status means they picked up - everything else needs retry
        numbers_to_retry = []
        successful_numbers = []
        
        for phone_number, latest_call in calls_by_number.items():
            latest_status = latest_call.get('status', 'initiated')
            if latest_status == 'answered':
                # They picked up! No retry needed.
                successful_numbers.append(phone_number)
            else:
                # Any status other than 'answered' needs retry
                numbers_to_retry.append({
                    'phone_number': phone_number,
                    'contact_name': latest_call.get('contact_name', 'Unknown'),
                    'status': latest_status,
                    'last_attempt': latest_call.get('timestamp', ''),
                    'duration': latest_call.get('call_duration', 0)
                })
        
        retry_count = len(numbers_to_retry)
        successful_count = len(successful_numbers)
        total_contacts = len(campaign.get('contacts', []))
        
        logger.info(f"📊 Retry analysis for {campaign_id}:")
        logger.info(f"   - Total contacts: {total_contacts}")
        logger.info(f"   - Numbers with calls: {len(calls_by_number)}")
        logger.info(f"   - Successful calls: {successful_count}")
        logger.info(f"   - Numbers needing retry: {retry_count}")
        
        if retry_count == 0:
            return jsonify({
                'success': True,
                'message': f'All {successful_count} contacts have been successfully reached! No retry needed.',
                'retry_count': 0,
                'successful_count': successful_count,
                'campaign_restarted': False,
                'statistics': {
                    'total_contacts': total_contacts,
                    'successful_calls': successful_count,
                    'needs_retry': retry_count,
                    'completion_rate': f"{(successful_count / max(1, len(calls_by_number))) * 100:.1f}%"
                }
            })
        
        # Update campaign contacts to only include numbers that need retry
        # Build the retry contact list from the original campaign contacts
        original_contacts = campaign.get('contacts', [])
        retry_contacts = []
        
        for retry_info in numbers_to_retry:
            retry_phone = retry_info['phone_number']
            # Find the original contact data for this phone number
            original_contact = next(
                (contact for contact in original_contacts 
                 if contact.get('phone_number') == retry_phone),
                None
            )
            
            if original_contact:
                retry_contacts.append(original_contact)
            else:
                # Fallback: create minimal contact data
                retry_contacts.append({
                    'name': retry_info['contact_name'],
                    'phone_number': retry_phone,
                    'notes': f"Retry - previous status: {retry_info['status']}"
                })
        
        # Update campaign for recycling (following CALLBOT2.1 pattern)
        campaign['contacts'] = retry_contacts
        campaign['status'] = 'draft'  # Reset to allow restart
        campaign['retry_info'] = {
            'original_contact_count': total_contacts,
            'successful_calls': successful_count,
            'retry_count': retry_count,
            'retry_timestamp': datetime.now().isoformat()
        }
        
        # Save updated campaign
        save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        # Start the campaign with only failed numbers (following CALLBOT2.1 pattern)
        logger.info(f"🚀 Restarting campaign {campaign_id} with {retry_count} failed numbers")
        
        try:
            # Use the existing start_campaign function to restart with retry contacts
            # This is the same pattern as CALLBOT2.1's processCampaignBatch call after updating numbers_to_call
            start_response = start_campaign(campaign_id)
            
            # Check if the response indicates success
            if hasattr(start_response, 'status_code'):
                if start_response.status_code == 200:
                    campaign_restarted = True
                    message = f"Found {retry_count} failed/no-answer calls out of {len(calls_by_number)} total attempts. Restarting campaign with only failed numbers..."
                else:
                    # Get error details from the response
                    error_data = start_response.get_json() if hasattr(start_response, 'get_json') else {}
                    raise Exception(error_data.get('error', f'Campaign start returned status {start_response.status_code}'))
            else:
                # start_campaign returned a direct response (not a Flask response object)
                campaign_restarted = True
                message = f"Found {retry_count} failed/no-answer calls out of {len(calls_by_number)} total attempts. Restarting campaign with only failed numbers..."
            
        except Exception as start_error:
            logger.error(f"❌ Error restarting campaign for retry: {start_error}")
            # Restore original campaign state on failure
            campaign['contacts'] = original_contacts
            campaign['status'] = 'completed'  # Set back to previous state
            save_json_data(CAMPAIGNS_FILE, campaigns_storage)
            
            return jsonify({
                'success': False,
                'error': 'Failed to restart campaign for retry',
                'message': str(start_error),
                'retry_count': retry_count
            }), 500
        
        logger.info(f"✅ Successfully started retry for campaign {campaign_id} with {retry_count} numbers")
        return jsonify({
            'success': True,
            'message': message,
            'retry_count': retry_count,
            'successful_count': successful_count,
            'campaign_restarted': campaign_restarted,
            'statistics': {
                'total_original_contacts': total_contacts,
                'successful_calls': successful_count,
                'needs_retry': retry_count,
                'retry_rate': f"{(retry_count / max(1, len(calls_by_number))) * 100:.1f}%",
                'success_rate': f"{(successful_count / max(1, len(calls_by_number))) * 100:.1f}%"
            },
            'retry_details': {
                'numbers_being_retried': [info['phone_number'] for info in numbers_to_retry],
                'retry_reasons': {info['phone_number']: info['status'] for info in numbers_to_retry}
            }
        })
                        
    except Exception as e:
        logger.error(f"❌ Error in retry unanswered calls: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': 'Failed to process retry request',
            'message': str(e)
        }), 500

@app.route('/api/campaigns/<campaign_id>/clear-logs', methods=['DELETE'])
def clear_campaign_logs(campaign_id):
    """Clear all call logs for a specific campaign"""
    try:
        # Use database instead of in-memory storage
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Check if campaign exists
            cursor.execute("SELECT id FROM campaigns WHERE id = ?", (campaign_id,))
            if not cursor.fetchone():
                return jsonify({'error': 'Campaign not found'}), 404
            
            # Delete all call logs for this campaign
            cursor.execute("DELETE FROM call_logs WHERE campaign_id = ?", (campaign_id,))
            deleted_count = cursor.rowcount
            conn.commit()
            
            logger.info(f"✅ Cleared {deleted_count} call logs for campaign: {campaign_id}")
            
        return jsonify({
            'message': f'Successfully cleared {deleted_count} call logs for campaign',
            'campaign_id': campaign_id,
            'deleted_count': deleted_count
        })
        
    except Exception as e:
        logger.error(f"❌ Error clearing campaign logs: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/reset', methods=['POST'])
def reset_campaign(campaign_id):
    """Reset a campaign to its initial draft state"""
    try:
        # Use database instead of in-memory storage
        with qc_db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Check if campaign exists
            cursor.execute("SELECT id, name FROM campaigns WHERE id = ?", (campaign_id,))
            campaign = cursor.fetchone()
            if not campaign:
                return jsonify({'error': 'Campaign not found'}), 404
            
            # Reset campaign status and clear timestamps (only update columns that exist)
            cursor.execute("""
                UPDATE campaigns 
                SET status = 'draft',
                    started_at = NULL,
                    completed_at = NULL,
                    paused_at = NULL,
                    resumed_at = NULL,
                    updated_at = ?
                WHERE id = ?
            """, (datetime.now().isoformat(), campaign_id))
            
            # Delete all call logs for this campaign
            cursor.execute("DELETE FROM call_logs WHERE campaign_id = ?", (campaign_id,))
            deleted_logs_count = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"✅ Campaign {campaign_id} reset successfully. Cleared {deleted_logs_count} call logs.")
            
        return jsonify({
            'success': True,
            'message': 'Campaign reset successfully',
            'campaign_id': campaign_id,
            'campaign_name': campaign[1] if campaign else campaign_id,
            'deleted_logs_count': deleted_logs_count
        })
        
    except Exception as e:
        logger.error(f"❌ Error resetting campaign {campaign_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Load environment variables at startup
    load_env_variables()
    
    # Start the server
    app.run(host='0.0.0.0', port=9090, debug=True, use_reloader=False)

