#!/usr/bin/env python3
"""
RADICAL APPROACH test - Trying completely different strategies to eliminate "tuk tuk" sounds.
"""

import os
import sys
import logging
import async<PERSON>

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from orpheus.tts import OrpheusTTS, Voice

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_radical_approach():
    """Test the radical approach with different strategies"""
    logger.info("🧪 Testing RADICAL APPROACH...")
    
    try:
        # Create TTS instance with smaller chunks
        voice = Voice(id="tara", name="Tara")
        tts = OrpheusTTS(voice=voice, chunk_size_bytes=1024)  # Much smaller chunks
        
        # Test text
        test_text = "Radical approach testing with minimal processing and small chunks."
        
        logger.info(f"Testing TTS synthesis with text: '{test_text}'")
        logger.info("🎯 RADICAL APPROACH strategies:")
        logger.info("  - 1KB chunks instead of 8KB (reduce buffering)")
        logger.info("  - Raw chunk pushing (no AudioByteStream)")
        logger.info("  - Minimal WAV header processing")
        logger.info("  - Direct frame creation for coordinated synthesis")
        
        # Create synthesis stream
        stream = tts.synthesize(test_text)
        
        logger.info("✅ Radical approach TTS stream created successfully")
        
        # Close the stream
        await stream.aclose()
        
        logger.info("✅ Radical approach test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Radical approach test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chunk_size_theory():
    """Test the theory that smaller chunks reduce artifacts"""
    logger.info("🧪 Testing chunk size theory...")
    
    try:
        logger.info("✅ Chunk size theory:")
        logger.info("  - Large chunks (8KB): More buffering, potential artifacts")
        logger.info("  - Small chunks (1KB): Less buffering, smoother streaming")
        logger.info("  - Theory: Smaller chunks = less chance for artifacts")
        logger.info("  - HTTP chunks: 1024 bytes")
        logger.info("  - Processing chunks: 1024 bytes")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chunk size theory test failed: {e}")
        return False

def test_processing_theory():
    """Test the theory that less processing reduces artifacts"""
    logger.info("🧪 Testing processing theory...")
    
    try:
        logger.info("✅ Processing theory:")
        logger.info("  - AudioByteStream: Adds processing overhead")
        logger.info("  - Raw chunks: Minimal processing")
        logger.info("  - Theory: Less processing = fewer artifacts")
        logger.info("  - Main synthesis: Raw chunk pushing")
        logger.info("  - Coordinated synthesis: Direct frame creation")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Processing theory test failed: {e}")
        return False

async def main():
    """Run all radical approach tests"""
    logger.info("🚀 Starting RADICAL APPROACH tests...")
    logger.info("🎯 Goal: Try completely different strategies!")
    
    tests = [
        ("Chunk Size Theory", test_chunk_size_theory),
        ("Processing Theory", test_processing_theory),
        ("Radical Approach Implementation", test_radical_approach),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("RADICAL APPROACH TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL RADICAL APPROACH TESTS PASSED!")
        logger.info("")
        logger.info("🎯 RADICAL STRATEGIES IMPLEMENTED:")
        logger.info("  1. Much smaller chunks (1KB instead of 8KB)")
        logger.info("  2. Raw chunk pushing (no AudioByteStream)")
        logger.info("  3. Minimal processing overhead")
        logger.info("  4. Direct frame creation where needed")
        logger.info("")
        logger.info("💥 IF THIS DOESN'T WORK, THE ISSUE MIGHT BE:")
        logger.info("  - In the Baseten API audio output itself")
        logger.info("  - In the network/HTTP streaming")
        logger.info("  - In the LiveKit output_emitter")
        logger.info("  - Something fundamental we haven't considered")
        logger.info("")
        logger.info("🔥 Try this approach and let me know the results!")
    else:
        logger.error("⚠️ Some tests failed.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
