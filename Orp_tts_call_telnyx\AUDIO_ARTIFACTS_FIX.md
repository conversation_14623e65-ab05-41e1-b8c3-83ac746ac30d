# Audio Artifacts Fix - TTS Hz Conversion Issues

## Problem Analysis

The "tuk tuk" sounds and audio artifacts in your custom TTS plugin were caused by several issues:

### 1. **Sample Rate Mismatch** 
- **Issue**: Your TTS service was converting audio to 22050 Hz, but LiveKit expects 24000 Hz
- **Impact**: Resampling artifacts, audio distortion, timing issues

### 2. **Multiple Format Conversions**
- **Issue**: Audio was being converted multiple times through the pipeline
- **Impact**: Quality degradation, introduction of artifacts

### 3. **Improper Chunk Alignment**
- **Issue**: Audio chunks weren't aligned to sample boundaries
- **Impact**: Audio cutting, "tuk tuk" sounds at chunk boundaries

### 4. **WAV Header Processing Issues**
- **Issue**: Inconsistent WAV header validation and removal
- **Impact**: Audio format mismatches, playback issues

## Fixes Applied

### 1. **Standardized Sample Rate to 24000 Hz**

**File**: `tts_service.py`
```python
# BEFORE (causing artifacts)
def create_wav_header(data_length, sample_rate=22050, channels=1, bits_per_sample=16):

# AFTER (LiveKit compatible)
def create_wav_header(data_length, sample_rate=24000, channels=1, bits_per_sample=16):
```

**File**: `tts_service.py` - Audio conversion
```python
# BEFORE
standardized_audio = audio.set_frame_rate(22050).set_channels(1).set_sample_width(2)
"-ar", "22050",  # Force sample rate

# AFTER
standardized_audio = audio.set_frame_rate(24000).set_channels(1).set_sample_width(2)
"-ar", "24000",  # FIXED: Force 24000 Hz sample rate (LiveKit standard)
```

### 2. **Improved Audio Processing Pipeline**

**File**: `orpheus/tts.py`
```python
# FIXED: Create audio byte stream with proper format validation
bstream = utils.audio.AudioByteStream(
    sample_rate=24000,  # LiveKit standard
    num_channels=1,
    bytes_per_sample=2  # 16-bit audio
)
```

### 3. **Enhanced Chunk Alignment**

**File**: `orpheus/tts.py`
```python
# FIXED: Ensure chunk is aligned to 16-bit samples (even number of bytes)
if len(chunk_to_process) % 2 != 0:
    # Put the odd byte back in the buffer
    audio_buffer = chunk_to_process[-1:] + audio_buffer
    chunk_to_process = chunk_to_process[:-1]
```

### 4. **Better WAV Header Validation**

**File**: `orpheus/tts.py`
```python
# FIXED: Validate WAV header format
try:
    fmt_chunk = wav_header[20:36]  # fmt chunk data
    audio_format, channels, sample_rate, byte_rate, block_align, bits_per_sample = struct.unpack('<HHIIHH', fmt_chunk)
    
    if sample_rate != 24000:
        logger.warning(f"⚠️ Sample rate mismatch: {sample_rate} Hz (expected 24000 Hz)")
    if channels != 1:
        logger.warning(f"⚠️ Channel count mismatch: {channels} (expected 1)")
    if bits_per_sample != 16:
        logger.warning(f"⚠️ Bit depth mismatch: {bits_per_sample} (expected 16)")
except Exception as e:
    logger.warning(f"⚠️ Could not validate WAV header: {e}")
```

### 5. **Optimized Frame Processing**

**File**: `orpheus/tts.py`
```python
# FIXED: Initialize output emitter with LiveKit-optimized settings
output_emitter.initialize(
    request_id=request_id,
    sample_rate=24000,  # LiveKit standard
    num_channels=1,     # Mono
    mime_type="audio/pcm",  # FIXED: Use PCM for better compatibility
    frame_size_ms=20,   # FIXED: 20ms frames for better stability
    stream=True,
)
```

## Testing the Fixes

Run the validation script to test the fixes:

```bash
cd Orp_tts_call_telnyx
python test_audio_format_fix.py
```

This script will:
1. Validate WAV header creation with correct 24000 Hz format
2. Test audio conversion pipeline
3. Check sample rate consistency across all components
4. Verify Orpheus TTS configuration

## Additional Fixes Applied (Round 2)

After initial testing revealed persistent "tuk tuk" sounds, additional critical fixes were applied:

### 6. **Fixed AudioByteStream Usage**

**Issue**: Using `write()` instead of `push()` method and missing frame size configuration
```python
# BEFORE (causing artifacts)
bstream = utils.audio.AudioByteStream(sample_rate=24000, num_channels=1)
for frame in bstream.write(chunk_to_process):

# AFTER (proper frame handling)
samples_per_frame = 24000 // 50  # 20ms frames = 480 samples
bstream = utils.audio.AudioByteStream(
    sample_rate=24000,
    num_channels=1,
    samples_per_channel=samples_per_frame
)
for frame in bstream.push(chunk_to_process):
```

### 7. **Proper Audio Frame Processing**

**Issue**: Direct chunk pushing without proper frame alignment
```python
# BEFORE (causing "tuk tuk" sounds)
output_emitter.push(chunk)

# AFTER (proper frame processing)
for frame in audio_bstream.push(chunk):
    output_emitter.push(frame.data)
```

### 8. **Reduced Silence Padding**

**Issue**: Excessive silence padding causing artifacts
```python
# BEFORE
silence_frame = self._create_silence_frame(duration_ms=100)

# AFTER
silence_frame = self._create_silence_frame(duration_ms=20)
```

### 9. **Simplified Silence Generation**

**Issue**: Complex fade effects in silence frames causing artifacts
```python
# BEFORE (complex fade effects)
amplitude = int((i / fade_samples) * 32)

# AFTER (pure silence)
silence_data = b'\x00' * (total_samples * 2)
```

## Expected Results

After applying these fixes, you should see:

### ✅ **Eliminated Audio Artifacts**
- No more "tuk tuk" sounds
- Smoother audio transitions
- Consistent audio quality
- Proper frame alignment

### ✅ **Improved Performance**
- Reduced processing overhead
- Better memory usage
- Faster audio generation
- Consistent 20ms frame processing

### ✅ **Better Compatibility**
- Consistent with LiveKit standards
- Proper AudioByteStream usage
- Reliable audio playback
- Correct frame size handling

## Key Technical Changes

1. **Sample Rate**: 22050 Hz → 24000 Hz (LiveKit standard)
2. **Chunk Size**: Optimized to 8KB with proper alignment
3. **Frame Size**: 10ms → 20ms for better stability
4. **Audio Format**: Enhanced PCM processing with validation
5. **Padding**: Reduced from 100ms to 50ms to minimize artifacts

## Monitoring

To monitor the fixes in production:

1. Check logs for sample rate warnings
2. Monitor audio quality in calls
3. Watch for chunk alignment warnings
4. Verify WAV header validation messages

## Rollback Plan

If issues occur, you can temporarily revert by:

1. Changing sample rates back to 22050 Hz in `tts_service.py`
2. Reverting chunk processing in `orpheus/tts.py`
3. Using the backup files created during the fix process

The fixes maintain backward compatibility while resolving the Hz conversion issues that were causing audio artifacts.
