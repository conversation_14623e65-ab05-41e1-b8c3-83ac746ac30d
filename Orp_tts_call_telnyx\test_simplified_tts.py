#!/usr/bin/env python3
"""
Test script for simplified TTS implementation without complex audio processing.
This follows the pattern used by successful TTS implementations like OpenAI and Spitch.
"""

import os
import sys
import logging
import asyncio

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from orpheus.tts import OrpheusTTS, Voice

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_simplified_tts():
    """Test the simplified TTS implementation"""
    logger.info("🧪 Testing simplified TTS implementation...")
    
    try:
        # Create TTS instance
        voice = Voice(id="tara", name="Tara")
        tts = OrpheusTTS(voice=voice, chunk_size_bytes=8192)  # Larger chunks for efficiency
        
        # Test text
        test_text = "Testing simplified audio processing without complex frame manipulation."
        
        logger.info(f"Testing TTS synthesis with text: '{test_text}'")
        logger.info("✅ Using simplified approach:")
        logger.info("  - Direct output_emitter.push() calls")
        logger.info("  - No AudioByteStream processing")
        logger.info("  - No artificial silence padding")
        logger.info("  - No fade-in/fade-out effects")
        logger.info("  - Standard WAV format")
        
        # Create synthesis stream
        stream = tts.synthesize(test_text)
        
        logger.info("✅ TTS stream created successfully")
        
        # Close the stream
        await stream.aclose()
        
        logger.info("✅ Simplified TTS test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Simplified TTS test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test TTS configuration matches successful implementations"""
    logger.info("🧪 Testing TTS configuration...")
    
    try:
        voice = Voice(id="tara", name="Tara")
        tts = OrpheusTTS(voice=voice)
        
        # Check configuration
        if tts._opts.sample_rate == 24000:
            logger.info("✅ Sample rate: 24000 Hz (correct)")
        else:
            logger.error(f"❌ Sample rate: {tts._opts.sample_rate} Hz (expected 24000)")
            return False
        
        if tts._opts.num_channels == 1:
            logger.info("✅ Channels: 1 (mono, correct)")
        else:
            logger.error(f"❌ Channels: {tts._opts.num_channels} (expected 1)")
            return False
        
        logger.info("✅ TTS configuration matches successful implementations")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False

async def main():
    """Run all simplified TTS tests"""
    logger.info("🚀 Starting simplified TTS tests...")
    
    tests = [
        ("TTS Configuration", test_configuration),
        ("Simplified TTS Implementation", test_simplified_tts),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All simplified TTS tests passed!")
        logger.info("💡 Key changes made:")
        logger.info("  - Removed complex AudioByteStream processing")
        logger.info("  - Eliminated artificial silence padding")
        logger.info("  - Removed fade-in/fade-out effects")
        logger.info("  - Using direct output_emitter.push() approach")
        logger.info("  - Standard WAV format like successful implementations")
        logger.info("📢 This should eliminate the 'tuk tuk' sounds!")
    else:
        logger.error("⚠️ Some tests failed.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
