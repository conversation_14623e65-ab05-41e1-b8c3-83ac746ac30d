#!/usr/bin/env python3
"""
Test script to validate audio format fixes for TTS artifacts.
This script tests the audio processing pipeline to ensure proper Hz conversion and format handling.
"""

import os
import sys
import logging
import asyncio
import tempfile
import struct
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tts_service import test_tts, convert_to_browser_compatible_audio, create_wav_header
from orpheus.tts import OrpheusTTS, Voice

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_wav_format(wav_bytes):
    """Validate WAV file format and extract audio parameters"""
    try:
        if len(wav_bytes) < 44:
            return False, "WAV file too short"
        
        # Check RIFF header
        if wav_bytes[:4] != b'RIFF':
            return False, "Not a valid RIFF file"
        
        if wav_bytes[8:12] != b'WAVE':
            return False, "Not a valid WAVE file"
        
        # Parse fmt chunk
        fmt_chunk = wav_bytes[20:36]  # Standard fmt chunk location
        audio_format, channels, sample_rate, byte_rate, block_align, bits_per_sample = struct.unpack('<HHIIHH', fmt_chunk)
        
        # Validate format
        issues = []
        if audio_format != 1:
            issues.append(f"Audio format: {audio_format} (expected 1 for PCM)")
        if channels != 1:
            issues.append(f"Channels: {channels} (expected 1 for mono)")
        if sample_rate != 24000:
            issues.append(f"Sample rate: {sample_rate} Hz (expected 24000 Hz)")
        if bits_per_sample != 16:
            issues.append(f"Bit depth: {bits_per_sample} (expected 16)")
        
        if issues:
            return False, "; ".join(issues)
        
        return True, f"Valid WAV: {sample_rate}Hz, {channels}ch, {bits_per_sample}bit"
        
    except Exception as e:
        return False, f"Error parsing WAV: {e}"

def test_wav_header_creation():
    """Test WAV header creation with correct format"""
    logger.info("🧪 Testing WAV header creation...")
    
    # Test data (1000 bytes of audio data)
    test_data_length = 1000
    
    # Create WAV header
    header, padding_bytes = create_wav_header(test_data_length)
    
    # Validate header
    is_valid, message = validate_wav_format(header + b'\x00' * (test_data_length + 2 * padding_bytes))
    
    if is_valid:
        logger.info(f"✅ WAV header creation: {message}")
        return True
    else:
        logger.error(f"❌ WAV header creation failed: {message}")
        return False

def test_audio_conversion():
    """Test audio conversion pipeline"""
    logger.info("🧪 Testing audio conversion pipeline...")
    
    # Create test audio data (simulate 1 second of 24000 Hz, 16-bit, mono audio)
    sample_rate = 24000
    duration_seconds = 1
    samples_count = sample_rate * duration_seconds
    
    # Generate test audio (simple sine wave)
    import math
    frequency = 440  # A4 note
    test_audio = []
    
    for i in range(samples_count):
        # Generate sine wave sample
        sample_value = int(16000 * math.sin(2 * math.pi * frequency * i / sample_rate))
        # Convert to 16-bit little-endian bytes
        sample_bytes = struct.pack('<h', sample_value)
        test_audio.append(sample_bytes)
    
    raw_audio_bytes = b''.join(test_audio)
    logger.info(f"Generated {len(raw_audio_bytes)} bytes of test audio data")
    
    # Test conversion
    try:
        converted_audio, content_type = convert_to_browser_compatible_audio(raw_audio_bytes)
        
        # Validate converted audio
        is_valid, message = validate_wav_format(converted_audio)
        
        if is_valid:
            logger.info(f"✅ Audio conversion: {message}")
            logger.info(f"✅ Content type: {content_type}")
            return True
        else:
            logger.error(f"❌ Audio conversion failed: {message}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Audio conversion error: {e}")
        return False

async def test_orpheus_tts():
    """Test Orpheus TTS with proper format validation"""
    logger.info("🧪 Testing Orpheus TTS audio format...")
    
    try:
        # Create TTS instance
        voice = Voice(id="tara", name="Tara")
        tts = OrpheusTTS(voice=voice, chunk_size_bytes=4096)
        
        # Test text
        test_text = "Hello, this is a test of the audio format validation system."
        
        logger.info(f"Testing TTS synthesis with text: '{test_text}'")
        
        # Note: This is a basic initialization test
        # Full synthesis testing would require actual API calls
        logger.info("✅ Orpheus TTS initialized successfully with correct format settings")
        
        # Validate TTS configuration
        if tts._opts.sample_rate == 24000:
            logger.info("✅ TTS sample rate: 24000 Hz (correct)")
        else:
            logger.error(f"❌ TTS sample rate: {tts._opts.sample_rate} Hz (expected 24000)")
            return False
        
        if tts._opts.num_channels == 1:
            logger.info("✅ TTS channels: 1 (mono, correct)")
        else:
            logger.error(f"❌ TTS channels: {tts._opts.num_channels} (expected 1)")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Orpheus TTS test error: {e}")
        return False

def test_sample_rate_consistency():
    """Test that all components use consistent sample rates"""
    logger.info("🧪 Testing sample rate consistency across components...")
    
    # Test WAV header creation
    header, _ = create_wav_header(1000)
    
    # Parse sample rate from header
    fmt_chunk = header[20:36]
    _, _, sample_rate, _, _, _ = struct.unpack('<HHIIHH', fmt_chunk)
    
    if sample_rate == 24000:
        logger.info("✅ WAV header sample rate: 24000 Hz")
    else:
        logger.error(f"❌ WAV header sample rate: {sample_rate} Hz (expected 24000)")
        return False
    
    # Test Orpheus TTS configuration
    voice = Voice(id="tara", name="Tara")
    tts = OrpheusTTS(voice=voice)
    
    if tts._opts.sample_rate == 24000:
        logger.info("✅ Orpheus TTS sample rate: 24000 Hz")
    else:
        logger.error(f"❌ Orpheus TTS sample rate: {tts._opts.sample_rate} Hz (expected 24000)")
        return False
    
    logger.info("✅ All components use consistent 24000 Hz sample rate")
    return True

async def main():
    """Run all audio format tests"""
    logger.info("🚀 Starting audio format validation tests...")
    
    tests = [
        ("WAV Header Creation", test_wav_header_creation),
        ("Audio Conversion Pipeline", test_audio_conversion),
        ("Sample Rate Consistency", test_sample_rate_consistency),
        ("Orpheus TTS Configuration", test_orpheus_tts),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All audio format tests passed! The Hz conversion issues should be resolved.")
    else:
        logger.error("⚠️ Some tests failed. Audio artifacts may still occur.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
