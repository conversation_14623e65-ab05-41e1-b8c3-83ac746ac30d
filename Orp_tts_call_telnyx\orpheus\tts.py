from __future__ import annotations

import asyncio
import dataclasses
import json
import uuid
import os
import base64
import weakref
from dataclasses import dataclass, field
from typing import Any, List, Literal, Optional

import aiohttp
from livekit import rtc
from livekit.agents import (
    DEFAULT_API_CONNECT_OPTIONS,
    APIConnectionError,
    APIConnectOptions,
    APIStatusError,
    APITimeoutError,
    tokenize,
    tts,
    utils,
)

from .log import logger
from .models import TTSEncoding, OrpheusTTSModels, OrpheusVoices

# Voice-specific API configurations
# FIXED: Correct URLs and endpoints based on user examples and Baseten documentation
VOICE_CONFIGS = {
    "tara": {
        "api_url": "https://model-4w7jnzyw.api.baseten.co/environments/production/predict",  # FIXED: Correct Tara URL and endpoint
        "api_key": "k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw",
        "is_streaming": True,  # Streaming mode
        "api_voice_name": "tara"  # FIXED: Lowercase for API
    },
    "elise": {
        "api_url": "https://model-5qenjjpq.api.baseten.co/environments/production/predict",  # FIXED: Correct Elise URL and endpoint
        "api_key": "k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw",
        "is_streaming": True,  # Streaming mode
        "api_voice_name": "elise"  # FIXED: Lowercase for API consistency
    },
    "tara_async": {
        "api_url": "https://model-4w7jnzyw.api.baseten.co/environments/production/predict",  # FIXED: Use synchronous endpoint like working version
        "api_key": "k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw",
        "is_streaming": True,  # FIXED: Use streaming mode like working version  
        "api_voice_name": "tara"  # FIXED: Lowercase for API, base name without _async
    },
    "elise_async": {
        "api_url": "https://model-5qenjjpq.api.baseten.co/environments/production/predict",  # FIXED: Use synchronous endpoint like working version
        "api_key": "k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw",
        "is_streaming": True,  # FIXED: Use streaming mode like working version
        "api_voice_name": "elise"  # FIXED: Lowercase for API consistency, base name without _async
    }
}

# Default configuration (Tara)
DEFAULT_API_URL = VOICE_CONFIGS["tara"]["api_url"]
DEFAULT_API_KEY = VOICE_CONFIGS["tara"]["api_key"]

def get_voice_config(voice_id: str):
    """Get API configuration for a specific voice"""
    voice_id_lower = voice_id.lower()
    if voice_id_lower in VOICE_CONFIGS:
        return VOICE_CONFIGS[voice_id_lower]
    else:
        # Default to Tara if voice not found
        logger.warning(f"Voice '{voice_id}' not found, defaulting to Tara")
        return VOICE_CONFIGS["tara"]

@dataclass
class VoiceSettings:
    # Placeholder for future voice settings
    pass

@dataclass
class Voice:
    id: str
    name: str
    settings: VoiceSettings | None = None

# Available voices
AVAILABLE_VOICES = [
    Voice(id="tara", name="Tara", settings=None),
    Voice(id="elise", name="Elise", settings=None),
    Voice(id="tara_async", name="Tara (Backup)", settings=None),
    Voice(id="elise_async", name="Elise (Backup)", settings=None),
]

# Default voice for Orpheus TTS
DEFAULT_VOICE = AVAILABLE_VOICES[0]  # Tara

def calculate_text_threshold_from_bytes(chunk_size_bytes: int) -> int:
    """
    Convert chunk size in bytes to text synthesis threshold in characters.
    
    FIXED: Larger thresholds to prevent audio cutting and reduce synthesis frequency.
    Larger chunks = fewer synthesis calls = better audio continuity and less interruption.
    
    This provides balanced chunking for natural speech flow:
    - 1024 bytes (conservative) → ~300 characters (complete sentences)
    - 4096 bytes (balanced) → ~500 characters (multiple sentences)
    - 8192 bytes (aggressive) → ~800 characters (paragraph chunks)
    
    Larger chunks ensure better audio continuity and reduce start/end cutoffs.
    """
    # CRITICAL FIX: Much larger thresholds to prevent audio cutting
    # Larger chunks = fewer interruptions but better audio quality
    if chunk_size_bytes <= 1024:
        return 300   # Conservative: wait for complete sentences
    elif chunk_size_bytes <= 4096:
        return 500   # Balanced: INCREASED to allow complete thoughts
    elif chunk_size_bytes <= 8192:
        return 800   # Aggressive: INCREASED for longer coherent passages
    else:
        return 1000  # Large: INCREASED for maximum coherence

@dataclass
class OrpheusTTSOptions:
    api_key: str = DEFAULT_API_KEY
    api_url: str = DEFAULT_API_URL
    voice: Voice = field(default_factory=lambda: DEFAULT_VOICE)
    model: OrpheusTTSModels = "orpheus"
    encoding: TTSEncoding = "pcm_24000"
    sample_rate: int = 24000
    num_channels: int = 1
    max_tokens: int = 10000
    chunk_size_bytes: int = 4096  # Default to balanced preset (4096 bytes)
    text_synthesis_threshold: int = 0  # Will be calculated from chunk_size_bytes
    word_tokenizer: tokenize.WordTokenizer = field(
        default_factory=lambda: tokenize.basic.WordTokenizer(ignore_punctuation=False)
    )

class OrpheusTTS(tts.TTS):
    def __init__(
        self,
        *,
        api_key: str | None = None,
        api_url: str | None = None,
        voice: Voice = DEFAULT_VOICE,
        model: OrpheusTTSModels = "orpheus",
        encoding: TTSEncoding = "pcm_24000",
        chunk_size_bytes: int = 4096,  # NEW: Accept chunk size configuration
        word_tokenizer: tokenize.WordTokenizer = tokenize.basic.WordTokenizer(ignore_punctuation=False),
        http_session: aiohttp.ClientSession | None = None,
    ) -> None:
        """
        Create a new instance of Orpheus TTS.
        
        Args:
            api_key: The API key for Baseten. If not provided, will use voice-specific API key.
            api_url: The API URL for the Baseten model. If not provided, will use voice-specific URL.
            voice: The voice to use for synthesis.
            model: The Orpheus model to use.
            encoding: The audio encoding.
            chunk_size_bytes: Size of audio chunks in bytes (affects text synthesis timing).
            word_tokenizer: The tokenizer to use for words.
            http_session: The HTTP session to use for requests.
        """
        # Get voice-specific configuration
        voice_config = get_voice_config(voice.id)
        
        # Use voice-specific settings if not explicitly provided
        if api_key is None:
            api_key = voice_config["api_key"]
        if api_url is None:
            api_url = voice_config["api_url"]
            
        # Store voice configuration info
        self._voice_config = voice_config
        self._is_streaming = voice_config["is_streaming"]
        
        logger.info(f"🎤 Initializing OrpheusTTS with voice: {voice.name} ({voice.id})")
        logger.info(f"🔄 Streaming mode: {'✅ ENABLED' if self._is_streaming else '❌ DISABLED (Non-streaming)'}")
        logger.info(f"🌐 API endpoint: {api_url}")
        
        # Calculate text synthesis threshold from chunk size
        text_synthesis_threshold = calculate_text_threshold_from_bytes(chunk_size_bytes)
        
        # Initialize parent class with TTS capabilities
        super().__init__(
            capabilities=tts.TTSCapabilities(
                streaming=self._is_streaming,  # Use voice-specific streaming capability
            ),
            sample_rate=24000,  # Orpheus uses 24000 Hz
            num_channels=1,  # Mono audio
        )

        self._opts = OrpheusTTSOptions(
            api_key=api_key,
            api_url=api_url,
            voice=voice,
            model=model,
            encoding=encoding,
            chunk_size_bytes=chunk_size_bytes,
            text_synthesis_threshold=text_synthesis_threshold,
            word_tokenizer=word_tokenizer,
        )
        self._session = http_session
        self._streams = weakref.WeakSet()  # Track active streams

        logger.info(f"🎯 Chunk size: {chunk_size_bytes} bytes → Text threshold: {text_synthesis_threshold} characters")

    async def _ensure_session(self) -> aiohttp.ClientSession:
        if not self._session:
            self._session = await _connection_pool.get_session()
        return self._session
    
    def prewarm(self) -> None:
        """Pre-warm connection pool for better performance"""
        logger.info("🔥 Pre-warming connection pool for Orpheus TTS")
        # This will be called by the agent to prepare connections
        pass
    
    async def aclose(self) -> None:
        """Close all active streams and connections"""
        for stream in list(self._streams):
            await stream.aclose()
        self._streams.clear()
        
        # Note: We don't close the global connection pool here
        # as it might be used by other TTS instances

    async def list_voices(self) -> List[Voice]:
        """Return list of available voices"""
        return AVAILABLE_VOICES

    def update_options(
        self,
        *,
        voice: Voice | None = None,
        model: OrpheusTTSModels | None = None,
    ) -> None:
        """
        Update the TTS options.
        
        Args:
            voice: The new voice to use.
            model: The new model to use.
        """
        if voice:
            self._opts.voice = voice
            # Update API configuration based on new voice
            voice_config = get_voice_config(voice.id)
            self._opts.api_key = voice_config["api_key"]
            self._opts.api_url = voice_config["api_url"]
            logger.info(f"Updated voice to {voice.id} with API URL: {self._opts.api_url}")
        if model:
            self._opts.model = model

    def synthesize(
        self,
        text: str,
        *,
        conn_options: APIConnectOptions = DEFAULT_API_CONNECT_OPTIONS,
    ) -> "OrpheusSynthesizeStream":
        """
        Synthesize speech from text using Orpheus TTS.
        
        Args:
            text: The text to synthesize.
            conn_options: Connection options for the API.
            
        Returns:
            A stream of synthesized audio.
        """
        # Log the text being synthesized
        logger.info(f"Synthesizing text: '{text[:100]}{'...' if len(text) > 100 else ''}' with voice {self._opts.voice.id}")
        
        # Check if text is empty, but don't replace it with a default
        # unless it's completely empty
        if not text or text.strip() == "":
            logger.warning("Empty text provided for synthesis, using default")
            text = "Hello, welcome to Orpheus TTS."
            
        # Create stream and register it
        stream = OrpheusSynthesizeStream(
            tts=self,
            conn_options=conn_options,
            opts=self._opts,
            session=None,  # Will be set async
            input_text=text,
        )
        self._streams.add(stream)
        return stream
        
    def stream(self) -> tts.SynthesizeStream:
        """
        Create a streaming synthesis session.
        
        This method is required by the LiveKit Agents framework for streaming TTS.
        
        Returns:
            A stream for synthesizing speech.
        """
        # When the agent pipeline requests a stream, it will later call `stream.write()` with the text.
        # But in some agent pipelines, a stream is created but text is never passed via write.
        # So we need to intercept the LLM response a different way.
        logger.info("Creating new streaming synthesis session for Orpheus TTS - using StreamingTTSBridge")
        stream = StreamingTTSBridge(
            tts=self,
            conn_options=DEFAULT_API_CONNECT_OPTIONS,
            session=None,  # Will be set async
            opts=self._opts,
        )
        self._streams.add(stream)
        return stream

# A bridge class to connect the agent pipeline's way of providing text with our TTS system
class StreamingTTSBridge(tts.ChunkedStream):
    """
    Smart streaming TTS bridge that handles text accumulation, intelligent chunking,
    and graceful shutdown without task destruction errors.
    """
    
    def __init__(
        self,
        *,
        tts: OrpheusTTS,
        opts: OrpheusTTSOptions,
        conn_options: APIConnectOptions,
        session: aiohttp.ClientSession,
    ) -> None:
        super().__init__(tts=tts, input_text="")
        self._conn_options = conn_options
        self._opts = opts
        self._session = session
        
        # Voice configuration
        self._is_streaming = tts._is_streaming
        self._voice_config = tts._voice_config
        
        # Text management with improved buffering
        self._buffer = ""
        self._input_ended = False
        self._processing_complete = False
        self._last_synthesis_time = 0
        
        # Synchronization
        self._text_ready = asyncio.Event()
        self._shutdown_event = asyncio.Event()
        
        # Task management
        self._main_task = None
        self._is_running = False
        
        # FIXED: Reduce logging noise - only log significant events
        self._char_count_since_log = 0
        self._log_interval = 50  # Log every 50 characters instead of every character
        
        logger.info("🎯 Initialized smart StreamingTTSBridge")
        
        # Validate event channel
        if hasattr(self, '_event_ch') and self._event_ch:
            logger.info(f"✅ Event channel confirmed: {type(self._event_ch)}")
        else:
            logger.error("❌ Event channel not available!")
    
    def push_text(self, text: str) -> None:
        """Add text to the synthesis buffer with reduced logging noise."""
        if self._input_ended:
            return
            
        if text:
            self._buffer += text
            self._char_count_since_log += len(text)
            
            # FIXED: Dramatically reduce logging noise - only log periodically
            if self._char_count_since_log >= self._log_interval:
                logger.debug(f"📝 Buffer update: +{self._char_count_since_log} chars, total: {len(self._buffer)} chars")
                self._char_count_since_log = 0
            
            # Signal that text is ready for processing
            self._text_ready.set()
    
    def end_input(self) -> None:
        """Mark input as complete."""
        logger.info("🏁 Input ended")
        self._input_ended = True
        self._text_ready.set()  # Wake up processor
    
    async def _run(self) -> None:
        """Main processing loop with smart error handling."""
        logger.info("🚀 Starting smart TTS processing")
        self._is_running = True
        
        try:
            # Start main processing task
            self._main_task = asyncio.create_task(self._process_text_smartly())
            await self._main_task
            
        except asyncio.CancelledError:
            logger.info("✅ Main task cancelled gracefully")
            raise  # Re-raise to properly handle cancellation
        except Exception as e:
            logger.error(f"❌ Error in main processing: {e}")
        finally:
            self._is_running = False
            self._processing_complete = True
            logger.info("✅ Smart TTS processing completed")
    
    async def _process_text_smartly(self) -> None:
        """Smart text processing with improved timing and sentence detection."""
        
        while not self._processing_complete:
            try:
                # Wait for text or shutdown signal with longer timeout
                done, pending = await asyncio.wait(
                    [
                        asyncio.create_task(self._text_ready.wait()),
                        asyncio.create_task(self._shutdown_event.wait())
                    ],
                    return_when=asyncio.FIRST_COMPLETED,
                    timeout=2.0  # INCREASED timeout to allow more text accumulation
                )
                
                # Cancel pending tasks
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                
                # Check for shutdown
                if self._shutdown_event.is_set():
                    logger.info("🛑 Shutdown signal received")
                    break
                
                # Process text if available
                if self._text_ready.is_set():
                    self._text_ready.clear()
                    
                    # FIXED: Much smarter synthesis decision logic
                    buffer_length = len(self._buffer)
                    contains_sentence_end = any(self._buffer.rstrip().endswith(p) for p in ['.', '!', '?'])
                    contains_pause_marker = any(marker in self._buffer for marker in [', ', '; ', ': ', ' - '])
                    
                    # Enhanced conditions for synthesis
                    should_process = (
                        self._input_ended or  # Input complete - always synthesize remaining
                        buffer_length > 800 or  # Buffer very large - prevent memory issues
                        (buffer_length > 200 and contains_sentence_end) or  # Complete sentence with good length
                        (buffer_length > 300 and contains_pause_marker) or  # Long phrase with natural pause
                        (buffer_length > 500)  # Moderately long text - synthesize for responsiveness
                    )
                    
                    # Add timing constraint to prevent too frequent synthesis
                    import time
                    current_time = time.time()
                    time_since_last = current_time - self._last_synthesis_time
                    min_synthesis_interval = 1.5  # Minimum 1.5 seconds between synthesis calls
                    
                    if should_process and self._buffer.strip():
                        if time_since_last >= min_synthesis_interval or self._input_ended:
                            await self._synthesize_buffer()
                            self._last_synthesis_time = current_time
                        else:
                            # Wait a bit more before synthesis
                            logger.debug(f"⏱️ Delaying synthesis: {time_since_last:.1f}s < {min_synthesis_interval}s")
                
                # Exit if input ended and buffer empty
                if self._input_ended and not self._buffer.strip():
                    logger.info("✅ All text processed, exiting")
                    break
                    
            except asyncio.TimeoutError:
                # Timeout is normal, continue loop
                continue
            except asyncio.CancelledError:
                logger.info("✅ Text processor cancelled")
                break
            except Exception as e:
                logger.error(f"❌ Error in text processing: {e}")
                await asyncio.sleep(0.1)
    
    async def _synthesize_buffer(self) -> None:
        """Synthesize current buffer content with improved error handling."""
        if not self._buffer.strip():
            return
            
        text = self._buffer.strip()
        self._buffer = ""  # Clear buffer
        
        logger.info(f"🎵 Synthesizing: '{text[:100]}{'...' if len(text) > 100 else ''}' ({len(text)} chars)")
        
        try:
            # Add silence padding for first chunk to prevent cutting
            add_silence_padding = not hasattr(self, '_first_synthesis_done')
            if add_silence_padding:
                self._first_synthesis_done = True
                logger.info("🔇 Adding silence padding to prevent start cutting")
            
            # Directly synthesize using the TTS API without creating another stream
            await self._direct_synthesize(text, add_silence_padding)
            
            # Smart delay: shorter for final synthesis, longer for streaming
            if self._input_ended:
                await asyncio.sleep(0.1)  # Shorter delay for final synthesis
            else:
                await asyncio.sleep(0.2)  # Moderate delay for streaming synthesis
            
        except Exception as e:
            logger.error(f"❌ Synthesis error: {e}")
    
    async def _direct_synthesize(self, text: str, add_silence_padding: bool = False) -> None:
        """Directly synthesize text using the Orpheus API."""
        # Get session if not provided
        if not self._session:
            self._session = await self._tts._ensure_session()
        
        # Generate a unique request ID
        request_id = str(uuid.uuid4())
        
        # Prepare the request payload
        api_voice_name = self._voice_config["api_voice_name"]
        
        if self._is_streaming:
            payload = {
                'voice': api_voice_name,
                'prompt': text,
                'max_tokens': self._opts.max_tokens
            }
        else:
            payload = {
                'model_input': {
                    'voice': api_voice_name,
                    'prompt': text,
                    'max_tokens': self._opts.max_tokens
                }
            }
        
        headers = {"Authorization": f"Api-Key {self._opts.api_key}"}
        headers.update({
            "Connection": "keep-alive",
            "Accept": "audio/wav, audio/*, */*",
            "User-Agent": "OrpheusTTS/1.0"
        })
        
        logger.info(f"🌐 Sending request to Orpheus API: {self._opts.api_url}")
        
        try:
            async with self._session.post(
                self._opts.api_url,
                headers=headers,
                json=payload,
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"Orpheus TTS API error: {resp.status} - {error_text}")
                    return
                
                # SIMPLIFIED: Remove AudioByteStream processing - use direct output_emitter approach
                
                frame_count = 0
                header_removed = False
                
                # REMOVED: No artificial silence padding - let LiveKit handle natural transitions
                
                # Buffer for smooth audio processing
                audio_buffer = b''
                first_chunk = True
                
                # ULTRA-SIMPLIFIED: Use consistent 8KB chunks throughout
                async for chunk in resp.content.iter_chunked(8192):
                    if chunk:
                        # ULTRA-SIMPLIFIED: Just remove WAV header if present
                        if first_chunk:
                            if chunk.startswith(b'RIFF') and len(chunk) >= 44:
                                # Simply skip the WAV header
                                chunk = chunk[44:]
                                logger.debug("🎵 WAV header removed")
                            first_chunk = False

                        # Add chunk to buffer
                        if chunk:
                            audio_buffer += chunk

                        # ULTRA-SIMPLIFIED: Process audio in larger, consistent chunks
                        chunk_size = 8192  # Use consistent 8KB chunks
                        while len(audio_buffer) >= chunk_size:
                            chunk_to_process = audio_buffer[:chunk_size]
                            audio_buffer = audio_buffer[chunk_size:]

                            # Skip odd-sized chunks to avoid artifacts
                            if len(chunk_to_process) % 2 != 0:
                                continue

                            # Send audio data directly as frame
                            if chunk_to_process:
                                samples_per_channel = len(chunk_to_process) // 2
                                audio_frame = rtc.AudioFrame(
                                    data=chunk_to_process,
                                    sample_rate=24000,
                                    num_channels=1,
                                    samples_per_channel=samples_per_channel
                                )
                                self._send_frame_safely(audio_frame, f"audio_{frame_count}")
                                frame_count += 1
                
                # ULTRA-SIMPLIFIED: Process remaining audio only if it's properly aligned
                if audio_buffer and len(audio_buffer) % 2 == 0 and len(audio_buffer) >= 2:
                    # Only process if we have properly aligned audio data
                    samples_per_channel = len(audio_buffer) // 2
                    final_frame = rtc.AudioFrame(
                        data=audio_buffer,
                        sample_rate=24000,
                        num_channels=1,
                        samples_per_channel=samples_per_channel
                    )
                    self._send_frame_safely(final_frame, f"final_{frame_count}")
                    frame_count += 1
                
                # REMOVED: No artificial fade-out effects - let LiveKit handle natural audio endings
                
                logger.info(f"✅ Synthesis complete: {frame_count} frames sent")
                
        except Exception as e:
            logger.error(f"❌ Direct synthesis error: {e}")
    
    def _create_silence_frame(self, duration_ms: int = 20) -> rtc.AudioFrame | None:
        """Create a minimal silence frame for padding."""
        try:
            # Calculate samples for the requested duration
            samples_per_ms = 24  # 24 samples per ms at 24kHz
            total_samples = duration_ms * samples_per_ms

            # FIXED: Create pure silence (no fade effects that might cause artifacts)
            # Just create zero-filled audio data
            silence_data = b'\x00' * (total_samples * 2)  # 2 bytes per 16-bit sample

            return rtc.AudioFrame(
                data=silence_data,
                sample_rate=24000,
                num_channels=1,
                samples_per_channel=total_samples
            )
        except Exception as e:
            logger.error(f"Failed to create silence frame: {e}")
            return None
    
    def _apply_fade_out(self, frame: rtc.AudioFrame, fade_strength: float = 0.2) -> rtc.AudioFrame:
        """Apply very gentle fade-out to audio frame to prevent abrupt endings."""
        try:
            # FIXED: Much gentler fade-out to avoid artifacts
            # Only fade the last 10% of the frame
            frame_data = bytearray(frame.data)
            samples_count = len(frame_data) // 2  # 16-bit samples
            fade_start = int(samples_count * 0.9)  # Start fade at 90% through the frame

            # Apply very gentle fade-out only to the end
            for i in range(fade_start, samples_count):
                sample_idx = i * 2
                if sample_idx + 1 < len(frame_data):
                    # Extract 16-bit sample (little-endian)
                    sample = int.from_bytes(frame_data[sample_idx:sample_idx+2], byteorder='little', signed=True)

                    # Apply very gentle fade-out
                    fade_progress = (i - fade_start) / (samples_count - fade_start)
                    fade_factor = 1.0 - (fade_strength * fade_progress)
                    faded_sample = int(sample * fade_factor)

                    # Clamp to 16-bit range
                    faded_sample = max(-32768, min(32767, faded_sample))

                    # Write back as 16-bit little-endian
                    faded_bytes = faded_sample.to_bytes(2, byteorder='little', signed=True)
                    frame_data[sample_idx:sample_idx+2] = faded_bytes

            # Create new frame with faded audio
            return rtc.AudioFrame(
                data=bytes(frame_data),
                sample_rate=frame.sample_rate,
                num_channels=frame.num_channels,
                samples_per_channel=frame.samples_per_channel
            )
        except Exception as e:
            logger.error(f"Failed to apply fade-out: {e}")
            return frame  # Return original frame on error
    
    def _send_frame_safely(self, frame: rtc.AudioFrame, frame_id: str) -> None:
        """Send frame with error handling."""
        try:
            if hasattr(self, '_event_ch') and self._event_ch:
                self._event_ch.send_nowait(
                    tts.SynthesizedAudio(
                        request_id=frame_id,
                        frame=frame,
                    )
                )
            else:
                logger.error("❌ No event channel available for frame transmission")
        except Exception as e:
            logger.debug(f"Frame {frame_id} send failed: {e}")
    
    async def close(self) -> None:
        """Graceful shutdown with proper task cleanup."""
        logger.info("🔄 Closing StreamingTTSBridge")
        
        # Signal shutdown
        self._shutdown_event.set()
        self._processing_complete = True
        
        # Cancel main task with timeout
        if self._main_task and not self._main_task.done():
            logger.info("⏳ Cancelling main task...")
            self._main_task.cancel()
            
            try:
                await asyncio.wait_for(self._main_task, timeout=2.0)
                logger.info("✅ Main task cancelled successfully")
            except asyncio.TimeoutError:
                logger.warning("⚠️ Main task cancellation timed out")
            except asyncio.CancelledError:
                logger.info("✅ Main task cancelled")
            except Exception as e:
                logger.error(f"❌ Error during task cancellation: {e}")
        
        # Call parent cleanup
        await super().close()
        logger.info("✅ StreamingTTSBridge closed successfully")

class OrpheusSynthesizeStream(tts.ChunkedStream):
    """
    Stream that synthesizes speech using Orpheus TTS.
    Handles the streaming response from the Baseten API.
    """

    def __init__(
        self,
        *,
        tts: OrpheusTTS,
        opts: OrpheusTTSOptions,
        conn_options: APIConnectOptions,
        session: aiohttp.ClientSession,
        input_text: str = "",
    ) -> None:
        super().__init__(tts=tts, input_text=input_text, conn_options=conn_options)
        self._opts = opts
        self._session = session
        self._buffer = ""  # Buffer for text when using write method
        self._is_streaming_mode = input_text == ""  # True if created via stream() method
        
        # FIXED: Get streaming mode from parent TTS instance
        self._is_streaming = tts._is_streaming
        self._voice_config = tts._voice_config
        


    async def _run(self, output_emitter: tts.AudioEmitter) -> None:
        """
        Run the synthesis stream with improved audio processing.
        """
        # Get session if not provided
        if not self._session:
            self._session = await self._tts._ensure_session()
        
        # Generate a unique request ID for this synthesis request
        request_id = str(uuid.uuid4())
        
        # SIMPLIFIED: Use standard WAV format like successful TTS implementations
        output_emitter.initialize(
            request_id=request_id,
            sample_rate=24000,  # LiveKit standard
            num_channels=1,     # Mono
            mime_type="audio/wav",  # Standard WAV format
            stream=True,
        )
        
        # Prepare the text to synthesize
        text_to_synthesize = self.input_text
        
        # Log the raw input text for debugging
        logger.info(f"Raw input text: '{text_to_synthesize}'")
        
        # Check for agent transcript in the text
        if "agent_transcript" in text_to_synthesize:
            try:
                # This is a special case where we've received JSON data with the transcript
                data = json.loads(text_to_synthesize)
                if "agent_transcript" in data:
                    text_to_synthesize = data["agent_transcript"]
                    logger.info(f"Extracted agent transcript: '{text_to_synthesize[:100]}{'...' if len(text_to_synthesize) > 100 else ''}'")
            except json.JSONDecodeError:
                # Not JSON, just use the text as is
                logger.warning("Failed to parse text as JSON, using raw text")
        
        # Only use default text if input is completely empty
        if not text_to_synthesize or text_to_synthesize.strip() == "":
            text_to_synthesize = "Hello, this is a test of the Orpheus Text to Speech system."
            logger.info(f"Using default text for synthesis: '{text_to_synthesize}'")
        else:
            # Make sure to use the actual input text
            logger.info(f"Synthesizing text: '{text_to_synthesize[:100]}{'...' if len(text_to_synthesize) > 100 else ''}'")
        
        # FIXED: Prepare the request payload based on streaming vs non-streaming mode
        # CRITICAL FIX: Use correct API voice name with proper case sensitivity
        api_voice_name = self._voice_config["api_voice_name"]
        
        if self._is_streaming:
            # Streaming mode: use direct payload format
            payload = {
                'voice': api_voice_name,  # FIXED: Use correct API voice name
                'prompt': text_to_synthesize,
                'max_tokens': self._opts.max_tokens
            }
            logger.info(f"📡 Using streaming mode payload format with voice: {api_voice_name}")
        else:
            # Non-streaming mode: wrap payload in model_input
            payload = {
                'model_input': {
                    'voice': api_voice_name,  # FIXED: Use correct API voice name
                    'prompt': text_to_synthesize,
                    'max_tokens': self._opts.max_tokens
                }
            }
            logger.info(f"📦 Using non-streaming mode payload format (wrapped in model_input) with voice: {api_voice_name}")
        
        # Prepare headers with API key
        headers = {"Authorization": f"Api-Key {self._opts.api_key}"}
        
        # FIXED: Add connection management headers for stable long audio synthesis  
        headers.update({
            "Connection": "keep-alive",
            "Accept": "audio/wav, audio/*, */*",
            "User-Agent": "OrpheusTTS/1.0"
        })
        
        logger.info(f"Sending request to Orpheus TTS API. URL: {self._opts.api_url}")
        logger.info(f"Request details - Voice: {self._opts.voice.id}, Text length: {len(text_to_synthesize)} chars")
        logger.info(f"Sample text: {text_to_synthesize[:50]}...")
        
        try:
            
            # Send the request to the API with streaming enabled - NO TIMEOUT
            # This approach is based on the ElevenLabs implementation
            async with self._session.post(
                self._opts.api_url,
                headers=headers,
                json=payload,
                # REMOVED timeout - let synthesis complete without time limits
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"Orpheus TTS API error: {resp.status} - {error_text}")
                    raise APIStatusError(f"Orpheus TTS API returned status {resp.status}: {error_text}")
                
                # Log the content type for debugging
                content_type = resp.headers.get('Content-Type', 'unknown')
                logger.info(f"Received response with Content-Type: {content_type}")
                
                # FIXED: Add Content-Length validation to detect truncated streams
                content_length = resp.headers.get('Content-Length')
                if content_length:
                    expected_bytes = int(content_length)
                    logger.info(f"Expected audio data: {expected_bytes} bytes")
                else:
                    logger.info("No Content-Length header - using chunked transfer encoding")
                
                # SIMPLIFIED: Process audio data directly like successful TTS implementations
                # Follow the pattern used by OpenAI, Spitch, and other working TTS plugins
                frame_count = 0
                header_removed = False  # Flag to track if WAV header has been removed
                total_bytes_received = 0  # Track total bytes to validate completion
                
                # ULTRA-SIMPLIFIED: Use consistent 8KB chunks
                async for chunk in resp.content.iter_chunked(8192):
                    if chunk:  # Only process if chunk has data
                        total_bytes_received += len(chunk)

                        # ULTRA-SIMPLIFIED: Just remove WAV header if present, no complex validation
                        if not header_removed:
                            if chunk.startswith(b'RIFF') and len(chunk) >= 44:
                                # Simply skip the standard 44-byte WAV header
                                chunk = chunk[44:]
                                logger.debug("🎵 WAV header removed")
                            header_removed = True

                        # ULTRA-SIMPLIFIED: Skip odd-sized chunks instead of padding (avoids artifacts)
                        if chunk and len(chunk) % 2 != 0:
                            logger.debug(f"⚠️ Skipping odd chunk size: {len(chunk)} bytes")
                            continue  # Skip this chunk to avoid artifacts

                        # SIMPLIFIED: Push audio data directly to output_emitter (like OpenAI TTS)
                        if chunk:
                            output_emitter.push(chunk)
                            frame_count += 1
                
                # FIXED: Validate stream completion
                logger.info(f"Generated {frame_count} frames from PCM data")
                logger.info(f"Total audio bytes received: {total_bytes_received}")
                
                if content_length and total_bytes_received < expected_bytes:
                    logger.warning(f"Stream potentially truncated! Expected {expected_bytes} bytes, received {total_bytes_received}")
                elif content_length and total_bytes_received >= expected_bytes:
                    logger.info(f"Stream complete: received all expected audio data")
                else:
                    logger.info(f"Stream complete: chunked encoding, received {total_bytes_received} bytes")
                
                # Flush the output emitter to ensure complete audio playback
                output_emitter.flush()
                logger.info(f"Generated {frame_count} audio chunks from API response")
                    
        # REMOVED timeout error handling - no more timeouts to handle
        except aiohttp.ClientError as e:
            logger.error(f"Orpheus TTS API connection error: {e}")
            raise APIConnectionError(f"Orpheus TTS API connection error: {e}")

# Add connection pool management
class ConnectionPool:
    """Simple connection pool for HTTP sessions"""
    
    def __init__(self, max_connections: int = 10, max_session_duration: float = 300.0):
        self._max_connections = max_connections
        self._max_session_duration = max_session_duration
        self._sessions: List[aiohttp.ClientSession] = []
        self._session_created_times: List[float] = []
        self._lock = asyncio.Lock()
    
    async def get_session(self) -> aiohttp.ClientSession:
        """Get a reusable HTTP session"""
        async with self._lock:
            import time
            current_time = time.time()
            
            # Remove expired sessions
            for i in range(len(self._sessions) - 1, -1, -1):
                if current_time - self._session_created_times[i] > self._max_session_duration:
                    await self._sessions[i].close()
                    del self._sessions[i]
                    del self._session_created_times[i]
            
            # Return existing session if available
            if self._sessions:
                return self._sessions[0]
            
            # Create new session
            session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=None, sock_connect=10.0),
                connector=aiohttp.TCPConnector(
                    limit=100,
                    limit_per_host=30,
                    keepalive_timeout=30,
                    enable_cleanup_closed=True
                )
            )
            
            self._sessions.append(session)
            self._session_created_times.append(current_time)
            
            return session
    
    async def close_all(self):
        """Close all sessions"""
        async with self._lock:
            for session in self._sessions:
                await session.close()
            self._sessions.clear()
            self._session_created_times.clear()

# Global connection pool
_connection_pool = ConnectionPool()
