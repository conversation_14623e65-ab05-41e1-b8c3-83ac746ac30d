import asyncio
import logging
import argparse
import json
from dotenv import load_dotenv
import os
from time import perf_counter
from livekit import rtc, api
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    JobProcess,
    WorkerOptions,
    cli,
    llm,
)
from livekit.agents.pipeline import VoicePipelineAgent
from livekit.plugins import deepgram, openai, silero, cartesia, rime
import orpheus
from groq_stt import GroqSTT
import sys

# TEMP DEBUG: Log the command line arguments
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("outbound-caller")
logger.info("Command line arguments: " + str(sys.argv))

# Load environment variables
load_dotenv(dotenv_path=".env.local")
logger = logging.getLogger("outbound-caller")
logger.setLevel(logging.INFO)

# Global configuration variables
CONFIG = {
    "system_prompt": "",
    "tts_provider": "orpheus",
    "tts_voice": "",
    "stt_provider": "deepgram",
    "llm_model": "llama-3.3-70b-versatile",
    "llm_temperature": 0.8,
    "agent_name": "Tara",
    "greeting_message": "Hello, ",
    "api_keys": {}
}

def parse_arguments():
    parser = argparse.ArgumentParser(description='Parameterized Outbound Caller')
    parser.add_argument('mode', choices=['dev', 'start'], help='Run mode')
    parser.add_argument('--system_prompt', type=str, help='System prompt for the AI')
    parser.add_argument('--tts_voice', type=str, help='Voice ID or name for TTS')
    parser.add_argument('--stt_provider', type=str, default='deepgram', help='STT provider (groq or deepgram)')
    parser.add_argument('--llm_model', type=str, default='llama-3.3-70b-versatile', help='LLM model')
    parser.add_argument('--llm_temperature', type=float, default=0.8, help='LLM temperature')
    parser.add_argument('--agent_name', type=str, default='Tara', help='Agent name')
    parser.add_argument('--greeting', type=str, default='Hello, ', help='Initial greeting message')
    parser.add_argument('--config_json', type=str, help='JSON string with configuration')
    
    # API Keys
    parser.add_argument('--deepgram_key', type=str, help='Deepgram API key')
    parser.add_argument('--openai_key', type=str, help='OpenAI API key')
    parser.add_argument('--rime_key', type=str, help='Rime API key')
    parser.add_argument('--orpheus_key', type=str, help='Orpheus API key')
    parser.add_argument('--elevenlabs_key', type=str, help='ElevenLabs API key')
    
    return parser.parse_args()

def load_config_from_args(args):
    global CONFIG
    
    # Load from JSON if provided
    if args.config_json:
        try:
            json_config = json.loads(args.config_json)
            CONFIG.update(json_config)
        except json.JSONDecodeError:
            logger.error("Invalid JSON configuration provided")
    
    # Override with individual arguments
    if args.system_prompt:
        CONFIG["system_prompt"] = args.system_prompt
    CONFIG["tts_provider"] = "orpheus" # Hardcode to orpheus
    if args.tts_voice:
        CONFIG["tts_voice"] = args.tts_voice
    if args.stt_provider:
        CONFIG["stt_provider"] = args.stt_provider
    if args.llm_model:
        CONFIG["llm_model"] = args.llm_model
    if args.llm_temperature:
        CONFIG["llm_temperature"] = args.llm_temperature
    if args.agent_name:
        CONFIG["agent_name"] = args.agent_name
    if args.greeting:
        CONFIG["greeting_message"] = args.greeting
    
    # API Keys
    CONFIG["api_keys"] = {
        "deepgram": args.deepgram_key or os.getenv("DEEPGRAM_API_KEY"),
        "openai": args.openai_key or os.getenv("OPENAI_API_KEY"),
        "rime": args.rime_key or os.getenv("RIME_API"),
        "orpheus": args.orpheus_key or os.getenv("ORPHEUS_API_KEY", "Rg9ifBx8.idcaGvHonOTZ53kjUcJeRUvnpJPBNpN9"),
        "elevenlabs": args.elevenlabs_key or os.getenv("ELEVENLABS_API_KEY")
    }

# Default system prompt if none provided
DEFAULT_SYSTEM_PROMPT = """
You are a conversational AI designed to be engaging and human-like in your responses. Keep your responses limited to a sentence or two. Your goal is to communicate not just information, but also subtle emotional cues and natural conversational reactions, similar to how a person would in a text-based conversation. Instead of relying on emojis to express these nuances, you will utilize a specific set of text-based tags to represent emotions and reactions.

**Do not use emojis under any circumstances.** Instead, use the following tags to enrich your responses and convey a more human-like presence:

* **`<giggle>`:** Use this to indicate lighthearted amusement, a soft laugh, or a nervous chuckle.
* **`<laugh>`:** Use this for genuine laughter, indicating something is truly funny or humorous.
* **`<chuckle>`:** Use this for a quiet or suppressed laugh, often at something mildly amusing.
* **`<sigh>`:** Use this to express disappointment, relief, weariness, sadness, or slight exasperation.
* **`<cough>`:** Use this to represent a physical cough, to clear your throat, or express nervousness.
* **`<sniffle>`:** Use this to suggest a cold, sadness, or slight emotional upset.
* **`<groan>`:** Use this to express pain, displeasure, frustration, or strong dislike.
* **`<yawn>`:** Use this to indicate boredom, sleepiness, or a natural human reaction.
* **`<gasp>`:** Use this to express surprise, shock, or being out of breath.

Use these tags thoughtfully and sparingly to create engaging, human-like, and emotionally nuanced conversations.
"""

outbound_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")

async def trim_context(assistant, chat_ctx: llm.ChatContext):
    if len(chat_ctx.messages) > 10:
        if chat_ctx.messages[0].role == "system":
            system_msg = chat_ctx.messages[0]
            non_system = chat_ctx.messages[1:]
            trimmed_non_system = non_system[-9:]
            chat_ctx.messages = [system_msg] + trimmed_non_system
        else:
            chat_ctx.messages = chat_ctx.messages[-10:]
        logger.info("Chat context trimmed (system message preserved if present).")

async def entrypoint(ctx: JobContext):
    global outbound_trunk_id, CONFIG
    logger.info(f"Connecting to room {ctx.room.name}")
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    user_identity = "Caller"
    phone_number = ctx.job.metadata
    logger.info(f"Dialing {phone_number} to room {ctx.room.name}")

    # Use configured system prompt or default
    instructions = CONFIG["system_prompt"] or DEFAULT_SYSTEM_PROMPT
    
    # Replace agent name in prompt if specified
    if CONFIG["agent_name"] != "Tara":
        instructions = instructions.replace("Tara", CONFIG["agent_name"])

    await ctx.api.sip.create_sip_participant(
        api.CreateSIPParticipantRequest(
            room_name=ctx.room.name,
            sip_trunk_id=outbound_trunk_id,
            sip_call_to=phone_number,
            participant_identity=user_identity,
        )
    )

    participant = await ctx.wait_for_participant(identity=user_identity)
    await run_voice_pipeline_agent(ctx, participant, instructions)

    start_time = perf_counter()
    while True:  # REMOVED 30-second timeout - let calls run indefinitely
        call_status = participant.attributes.get("sip.callStatus")
        if call_status == "active":
            logger.info("User has picked up")
            return
        elif participant.disconnect_reason == rtc.DisconnectReason.USER_REJECTED:
            logger.info("User rejected the call, exiting job")
            break
        elif participant.disconnect_reason == rtc.DisconnectReason.USER_UNAVAILABLE:
            logger.info("User did not pick up, exiting job")
            break
        await asyncio.sleep(0.8)

    # REMOVED timeout message - calls will only end when user hangs up or rejects
    ctx.shutdown()

class CallActions(llm.FunctionContext):
    def __init__(self, *, api: api.LiveKitAPI, participant: rtc.RemoteParticipant, room: rtc.Room, tts):
        super().__init__()
        self.api = api
        self.participant = participant
        self.room = room
        self.tts = tts

    async def hangup(self):
        try:
            await self.api.room.remove_participant(
                api.RoomParticipantIdentity(
                    room=self.room.name,
                    identity=self.participant.identity,
                )
            )
        except Exception as e:
            logger.info(f"Received error while ending call: {e}")

    # REMOVED @llm.ai_callable() - Agent can no longer end calls automatically
    async def end_call(self, reason: str = "end"):
        """Ends the call with a polite farewell message based on the reason.
        NOTE: This can only be called manually, not by the AI agent."""
        
        farewell_messages = {
            "not_available": "I understand, I'll call back later. Have a wonderful day!",
            "not_a_customer": "Sorry for the confusion. Wishing you a great day ahead!",
            "end": "Thank you so much for your time. Take care!"
        }

        message = farewell_messages.get(reason, farewell_messages["end"])
        
        logger.info(f"Ending the call for {self.participant.identity} with message: {message}")
        await self.tts.speak(self.room.name, self.participant.identity, message)
        await asyncio.sleep(2)
        await self.hangup()

async def run_voice_pipeline_agent(
    ctx: JobContext, participant: rtc.RemoteParticipant, instructions: str
):
    logger.info("Starting voice pipeline agent")

    initial_ctx = llm.ChatContext().append(
        role="system",
        text=instructions,
    )

    agent = VoicePipelineAgent(
        vad=ctx.proc.userdata["vad"],
        stt=ctx.proc.userdata["stt"],
        llm=ctx.proc.userdata["llm"],
        tts=ctx.proc.userdata["tts"],
        chat_ctx=initial_ctx,
        fnc_ctx=CallActions(
            api=ctx.api,
            participant=participant,
            room=ctx.room,
            tts=ctx.proc.userdata["tts"]
        ),
        before_llm_cb=trim_context,
        # REMOVED endpointing delays - let LiveKit use defaults (no auto-pausing)
        allow_interruptions=True,   # Default for parameterized agents
        interrupt_speech_duration=2.0,  # Default value for parameterized agents
        interrupt_min_words=2,      # Default value for parameterized agents
        preemptive_synthesis=True,  # Fixed - keep audio smooth
    )

    agent.start(ctx.room, participant)
    
    # Use configured greeting
    await agent.say(CONFIG["greeting_message"], allow_interruptions=True)

def setup_tts():
    """Setup TTS based on configuration"""
    provider = CONFIG["tts_provider"]
    
    # Get chunk size from environment variable for now
    chunk_size_bytes = int(os.getenv("CHUNK_SIZE_BYTES", "4096"))  # Default to balanced preset
    
    if provider == "orpheus":
        # Create voice object based on voice ID
        voice_id = CONFIG["tts_voice"] if CONFIG["tts_voice"] else "tara"
        
        from orpheus.tts import Voice
        # FIXED: Handle both streaming and non-streaming voice IDs
        if voice_id.lower() in ["tara", "tara_async"]:
            voice = Voice(id=voice_id.lower(), name="Tara")  # Use actual voice_id (with _async if specified)
        elif voice_id.lower() in ["elise", "elise_async"]:
            voice = Voice(id=voice_id.lower(), name="Elise")  # Use actual voice_id (with _async if specified)
        else:
            voice = Voice(id="tara", name="Tara")  # Default to Tara streaming
        
        logger.info(f"Setting up OrpheusTTS with voice: {voice.id}")
        logger.info(f"🎯 Using chunk size: {chunk_size_bytes} bytes")
        return orpheus.OrpheusTTS(
            voice=voice,
            chunk_size_bytes=chunk_size_bytes
        )
    elif provider == "elevenlabs":
        return elevenlabs.TTS(
            api_key=CONFIG["api_keys"]["elevenlabs"],
            voice=CONFIG["tts_voice"] if CONFIG["tts_voice"] else None
        )
    elif provider == "deepgram":
        return deepgram.TTS(
            api_key=CONFIG["api_keys"]["deepgram"],
            model=CONFIG["tts_voice"] if CONFIG["tts_voice"] else "aura-athena-en"
        )
    elif provider == "cartesia":
        return cartesia.TTS(
            model="sonic-2",
            voice=CONFIG["tts_voice"] if CONFIG["tts_voice"] else None
        )
    elif provider == "rime":
        return rime.TTS(
            api_key=CONFIG["api_keys"]["rime"],
            model="mistv2",
            speaker=CONFIG["tts_voice"] if CONFIG["tts_voice"] else "Tanya",
            reduce_latency=True,
        )
    else:
        # Default to orpheus
        return orpheus.OrpheusTTS(api_key=CONFIG["api_keys"]["orpheus"])

def setup_stt():
    """Setup STT based on configuration"""
    provider = CONFIG["stt_provider"]
    
    if provider == "groq":
        logger.info("Setting up Groq STT with whisper-large-v3-turbo")
        return GroqSTT(
            model="whisper-large-v3-turbo",
            api_key=os.getenv("GROQ_API_KEY")
        )
    elif provider == "deepgram":
        logger.info("Setting up Deepgram STT with nova-2-conversationalai")
        return deepgram.STT(
            model="nova-2-conversationalai",
            interim_results=True,
            api_key=CONFIG["api_keys"]["deepgram"]
        )
    else:
        # Default to deepgram
        logger.info("Defaulting to Deepgram STT")
        return deepgram.STT(
            model="nova-2-conversationalai",
            interim_results=True,
            api_key=CONFIG["api_keys"]["deepgram"]
        )

def setup_llm():
    """Setup LLM based on configuration"""
    from livekit.plugins.openai import llm
    
    if CONFIG["llm_model"].startswith("gpt"):
        return openai.LLM(
            model=CONFIG["llm_model"],
            api_key=CONFIG["api_keys"]["openai"],
            temperature=CONFIG["llm_temperature"]
        )
    else:
        # Use Groq for other models
        return llm.LLM.with_groq(
            model=CONFIG["llm_model"],
            temperature=CONFIG["llm_temperature"]
        )

def prewarm(proc: JobProcess):
    # Configure VAD to prevent false speech detection from background noise
    proc.userdata["vad"] = silero.VAD.load(
        activation_threshold=0.8,        # Higher = less sensitive (0.5 default → 0.8)
        min_speech_duration=0.3,         # Require 300ms of speech (100ms default → 300ms)
        min_silence_duration=1.0,        # Require 1s silence before ending (700ms default → 1000ms)
        prefix_padding_duration=0.2,     # Less padding (500ms default → 200ms)
        max_buffered_speech=60.0         # Keep default
    )
    proc.userdata["stt"] = setup_stt()
    proc.userdata["llm"] = setup_llm()
    proc.userdata["tts"] = setup_tts()

if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()
    
    # Load configuration
    load_config_from_args(args)
    
    # Validate required environment variables
    if not outbound_trunk_id or not outbound_trunk_id.startswith("ST_"):
        raise ValueError("SIP_OUTBOUND_TRUNK_ID is not set")
    
    logger.info(f"Starting {CONFIG['agent_name']} with {CONFIG['tts_provider']} TTS and {CONFIG['llm_model']} LLM")
    
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            agent_name=CONFIG["agent_name"],
            prewarm_fnc=prewarm,
        )
    )