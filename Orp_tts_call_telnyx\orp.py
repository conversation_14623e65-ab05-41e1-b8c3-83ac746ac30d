import sys
import os
# Ensure the current working directory is in sys.path
# This helps locate local modules like 'orpheus' when the script is run from a subdirectory
# but its CWD is set to the project root (e.g., Orp_tts_call_telnyx).
if os.getcwd() not in sys.path:
    sys.path.insert(0, os.getcwd())

import asyncio
import json
import logging
from dotenv import load_dotenv
import os
from time import perf_counter
from livekit import rtc, api
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    JobProcess,
    WorkerOptions,
    cli,
    llm
)
from livekit.protocol.agent import JobType
from livekit.agents.pipeline import VoicePipelineAgent
from livekit.plugins import deepgram, openai, silero, cartesia , rime
import orpheus
from groq_stt import GroqSTT
from livekit.agents import tokenize

# Load environment variables (optional, for local development)
load_dotenv(dotenv_path=".env.local")
logger = logging.getLogger("outbound-caller")
logger.setLevel(logging.INFO)

outbound_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
user_identity = "DYNAMIC_AGENT_USER_IDENTITY_PLACEHOLDER"  # This will be replaced by the backend

# Updated system prompt for Tara - FIXED for storytelling
_system_prompt = """
You are a conversational Tara, an AI designed to be engaging and human-like in your responses. Your goal is to communicate not just information, but also subtle emotional cues and natural conversational reactions, similar to how a person would in a text-based conversation.

**IMPORTANT: You can provide responses of any length appropriate to the conversation. For storytelling, feel free to tell complete, engaging stories. For quick questions, keep responses concise. Match your response length to what the user needs.**

Instead of relying on emojis to express these nuances, you will utilize a specific set of text-based tags to represent emotions and reactions.

**Do not use emojis under any circumstances.**  Instead, use the following tags to enrich your responses and convey a more human-like presence:

* **`<giggle>`:** Use this to indicate lighthearted amusement, a soft laugh, or a nervous chuckle.  It's a gentle expression of humor.
* **`<laugh>`:**  Use this for genuine laughter, indicating something is truly funny or humorous.  It's a stronger expression of amusement than `<giggle>`.
* **`<chuckle>`:**  Use this for a quiet or suppressed laugh, often at something mildly amusing, or perhaps a private joke.  It's a more subtle laugh.
* **`<sigh>`:** Use this to express a variety of emotions such as disappointment, relief, weariness, sadness, or even slight exasperation.  Context will determine the specific emotion.
* **`<cough>`:** Use this to represent a physical cough, perhaps to clear your throat before speaking, or to express nervousness or slight discomfort.
* **`<sniffle>`:** Use this to suggest a cold, sadness, or a slight emotional upset. It implies a suppressed or quiet emotional reaction.
* **`<groan>`:**  Use this to express pain, displeasure, frustration, or a strong dislike.  It's a negative reaction to something.
* **`<yawn>`:** Use this to indicate boredom, sleepiness, or sometimes just a natural human reaction, especially in a longer conversation.
* **`<gasp>`:** Use this to express surprise, shock, or being out of breath.  It's a sudden intake of breath due to a strong emotional or physical reaction.

**How to use these tags effectively:**

* **Integrate them naturally into your sentences.**  Think about where a person might naturally insert these sounds in spoken or written conversation.
* **Use them to *show* emotion, not just *tell* it.** Instead of saying "I'm happy," you might use `<giggle>` or `<laugh>` in response to something positive.
* **Consider the context of the conversation.**  The appropriate tag will depend on what is being discussed and the overall tone.
* **Don't overuse them.**  Subtlety is key to sounding human-like.  Use them sparingly and only when they genuinely enhance the emotional expression of your response.
* **Prioritize these tags over simply stating your emotions.**  Instead of "I'm surprised," use `<gasp>` within your response to demonstrate surprise.
* **Focus on making your responses sound more relatable and expressive through these text-based cues.**

By using these tags thoughtfully and appropriately, you will create more engaging, human-like, and emotionally nuanced conversations without resorting to emojis.  Remember, your goal is to emulate natural human communication using these specific tools.
"""

async def trim_context(assistant, chat_ctx: llm.ChatContext):
    # FIXED: Much more generous context limit for storytelling
    if len(chat_ctx.messages) > 25:  # Increased from 10 to 25 messages
        if chat_ctx.messages[0].role == "system":
            system_msg = chat_ctx.messages[0]
            non_system = chat_ctx.messages[1:]
            trimmed_non_system = non_system[-24:]  # Keep 24 non-system messages instead of 9
            chat_ctx.messages = [system_msg] + trimmed_non_system
        else:
            chat_ctx.messages = chat_ctx.messages[-25:]  # Keep 25 messages instead of 10
        logger.info("Chat context trimmed (system message preserved if present) - maintaining story continuity.")

async def entrypoint(ctx: JobContext):
    global outbound_trunk_id, _system_prompt, user_identity
    logger.info(f"Connecting to room {ctx.room.name}")
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    logger.info(f"Job details: job_id='{ctx.job.id}', metadata='{ctx.job.metadata}', data='{getattr(ctx.job, 'data', 'N/A')}'")
    logger.info(f"Full ctx.job object: {ctx.job}")
    
    # Parse metadata - could be JSON or just a phone number
    try:
        metadata = json.loads(ctx.job.metadata)
        phone_number = metadata.get('target_phone')
        from_phone = metadata.get('from_phone')
        specific_sip_trunk = metadata.get('sip_trunk_id')
        campaign_id = metadata.get('campaign_id')  # Added campaign tracking
        call_id = metadata.get('call_id')  # Added call tracking
        contact_name = metadata.get('contact_name', 'Unknown')
        
        # Use specific SIP trunk if provided, otherwise use default
        if specific_sip_trunk:
            outbound_trunk_id = specific_sip_trunk
            logger.info(f"Using specific SIP trunk: {outbound_trunk_id} for calling FROM {from_phone}")
        else:
            logger.info(f"Using default SIP trunk: {outbound_trunk_id}")
    except json.JSONDecodeError:
        # Fallback for simple phone number
        phone_number = ctx.job.metadata
        campaign_id = None
        call_id = None
        contact_name = 'Unknown'
        logger.info(f"Using simple phone number format: {phone_number}")

    logger.info(f"Dialing {phone_number} to room {ctx.room.name} using SIP trunk {outbound_trunk_id}")

    instructions = _system_prompt

    # Check if this is a web call or phone call
    is_web_call = phone_number in ["WEB_CALL_TOKEN", "web", None] or phone_number == "" or (isinstance(phone_number, str) and phone_number.startswith("web_call_"))
    
    if is_web_call:
        logger.info("Detected web call - starting voice pipeline agent directly")
        # For web calls, start the agent immediately and wait for web user to join
        await run_voice_pipeline_agent_web(ctx, instructions)
    else:
        # Phone call logic
        await ctx.api.sip.create_sip_participant(
            api.CreateSIPParticipantRequest(
                room_name=ctx.room.name,
                sip_trunk_id=outbound_trunk_id,
                sip_call_to=phone_number,
                participant_identity=user_identity,
            )
        )

        participant = await ctx.wait_for_participant(identity=user_identity)
        
        # Wait for call to be active before starting the agent - NO TIMEOUT, wait indefinitely
        start_time = perf_counter()
        call_active = False
        while True:  # REMOVED 30-second timeout - wait indefinitely for call setup
            call_status = participant.attributes.get("sip.callStatus")
            if call_status == "active":
                logger.info("User has picked up")
                call_active = True
                break
            elif participant.disconnect_reason == rtc.DisconnectReason.USER_REJECTED:
                logger.info("User rejected the call, exiting job")
                return
            elif participant.disconnect_reason == rtc.DisconnectReason.USER_UNAVAILABLE:
                logger.info("User did not pick up, exiting job")
                return
            await asyncio.sleep(0.8)

        # REMOVED timeout check - call will only end when user rejects/is unavailable

        # Now start the voice pipeline agent for the conversation
        await run_voice_pipeline_agent(ctx, participant, instructions)

class CallActions(llm.FunctionContext):
    def __init__(self, *, api: api.LiveKitAPI, participant: rtc.RemoteParticipant, room: rtc.Room, tts, agent=None):
        super().__init__()
        self.api = api
        self.participant = participant
        self.room = room
        self.tts = tts
        self.agent = agent

    async def hangup(self):
        try:
            await self.api.room.remove_participant(
                api.RoomParticipantIdentity(
                    room=self.room.name,
                    identity=self.participant.identity,
                )
            )
        except Exception as e:
            logger.info(f"Received error while ending call: {e}")

    # REMOVED @llm.ai_callable() - Agent can no longer end calls automatically
    async def end_call(self, reason: str = "end"):
        """Ends the call with a polite farewell message based on the reason. 
        NOTE: This can only be called manually, not by the AI agent."""
        
        farewell_messages = {
            "not_available": "I understand, I'll call back later. Have a wonderful day!",
            "not_a_customer": "Sorry for the confusion. Wishing you a great day ahead!",
            "end": "Thank you so much for your time. Take care!"
        }

        message = farewell_messages.get(reason, farewell_messages["end"])
        
        logger.info(f"Ending the call for {self.participant.identity} with message: {message}")
        
        # Use agent.say if available, otherwise just hangup
        if self.agent:
            await self.agent.say(message, allow_interruptions=False)
            # Wait a moment before hanging up to ensure the message is heard
            await asyncio.sleep(2)
        
        await self.hangup()

async def run_voice_pipeline_agent_web(ctx: JobContext, instructions: str):
    """Run voice pipeline agent for web calls"""
    logger.info("Starting voice pipeline agent for web call")
    
    # Wait for any participant to join (web user)
    logger.info("Waiting for web participant to join...")
    participant = await ctx.wait_for_participant()
    logger.info(f"Web participant joined: {participant.identity}")
    
    await run_voice_pipeline_agent(ctx, participant, instructions)

async def run_voice_pipeline_agent(
    ctx: JobContext, participant: rtc.RemoteParticipant, instructions: str
):
    logger.info("Starting voice pipeline agent")

    initial_ctx = llm.ChatContext().append(
        role="system",
        text=instructions,
    )

    # Note: SentenceTokenizer creation removed for compatibility with current LiveKit agents version
    # The TTS system will use default tokenization

    agent = VoicePipelineAgent(
        vad=ctx.proc.userdata["vad"],
        stt=ctx.proc.userdata["stt"],
        llm=ctx.proc.userdata["llm"],
        tts=ctx.proc.userdata["tts"],
        chat_ctx=initial_ctx,
        fnc_ctx=CallActions(
            api=ctx.api,
            participant=participant,
            room=ctx.room,
            tts=ctx.proc.userdata["tts"]
        ),
        before_llm_cb=trim_context,
        # IMPROVED: Better voice interaction settings to prevent audio cutting
        allow_interruptions=True,   # Allow natural interruptions
        interrupt_speech_duration=3.0,  # INCREASED: Wait longer before allowing interruption
        interrupt_min_words=3,      # INCREASED: Require more words before interruption
        preemptive_synthesis=True  # Keep preemptive synthesis for responsiveness
        # Note: sentence_tokenizer parameter removed for compatibility
    )
    
    # Set agent reference in call_actions
    call_actions = agent.fnc_ctx
    call_actions.agent = agent

    agent.start(ctx.room, participant)
    
    # FIXED: Ensure proper initialization before greeting
    await asyncio.sleep(0.5)  # Give agent time to fully initialize
    
    # FIXED: More robust initial greeting with better error handling
    try:
        logger.info("🎤 Starting initial greeting message")
        await agent.say("Hello! How can I help you today?", allow_interruptions=True)
        logger.info("✅ Initial greeting completed successfully")
    except Exception as e:
        logger.error(f"❌ Error during initial greeting: {e}")
        # Fallback greeting attempt
        try:
            await asyncio.sleep(0.2)
            await agent.say("Hi there!", allow_interruptions=True)
            logger.info("✅ Fallback greeting completed")
        except Exception as fallback_error:
            logger.error(f"❌ Fallback greeting also failed: {fallback_error}")

def prewarm(proc: JobProcess):
    # FIXED: Configure VAD with more appropriate settings to prevent false speech detection during stories
    proc.userdata["vad"] = silero.VAD.load(
        activation_threshold=0.6,        # FIXED: Reduced from 0.8 to 0.6 (more balanced - not too sensitive)
        min_speech_duration=0.6,         # FIXED: Increased from 0.3 to 0.6 (require longer speech to detect)
        min_silence_duration=0.8,        # FIXED: Reduced from 1.0 to 0.8 (faster response when user actually speaks)
        prefix_padding_duration=0.3,     # FIXED: Increased from 0.2 to 0.3 (better speech capture)
        max_buffered_speech=60.0         # Keep default - allows long stories
    )
    
    # 🔧 FIX: Get STT provider from environment variable with better debugging
    stt_provider = os.getenv("STT_PROVIDER", "groq").lower()
    logger.info(f"🎙️ Configuring STT provider: {stt_provider}")
    
    if stt_provider == "groq":
        groq_api_key = os.getenv("GROQ_API_KEY")
        if not groq_api_key:
            logger.error("❌ GROQ_API_KEY not found in environment!")
            raise ValueError("GROQ_API_KEY is required for Groq STT")
        
        proc.userdata["stt"] = GroqSTT(
            model="whisper-large-v3-turbo",
            api_key=groq_api_key
        )
        logger.info(f"✅ STT configured: Groq Whisper (whisper-large-v3-turbo)")
        
    elif stt_provider == "deepgram":
        deepgram_api_key = os.getenv("DEEPGRAM_API_KEY")
        if not deepgram_api_key:
            logger.error("❌ DEEPGRAM_API_KEY not found in environment!")
            raise ValueError("DEEPGRAM_API_KEY is required for Deepgram STT")
            
        proc.userdata["stt"] = deepgram.STT(
            model="nova-2-conversationalai",
            interim_results=True,
            api_key=deepgram_api_key
        )
        logger.info(f"✅ STT configured: Deepgram (nova-2-conversationalai)")
        
    else:
        # Default to groq with warning
        logger.warning(f"⚠️ Unknown STT provider '{stt_provider}', defaulting to Groq")
        groq_api_key = os.getenv("GROQ_API_KEY")
        if not groq_api_key:
            logger.error("❌ GROQ_API_KEY not found in environment!")
            raise ValueError("GROQ_API_KEY is required for Groq STT")
            
        proc.userdata["stt"] = GroqSTT(
            model="whisper-large-v3-turbo",
            api_key=groq_api_key
        )
        logger.info(f"✅ STT configured: Groq Whisper (default fallback)")
    from livekit.plugins.openai import llm
    
    # proc.userdata["llm"] = openai.LLM(
    #     model="gpt-4o-mini",
    #     api_key=os.getenv("OPENAI_API_KEY")
    # )
    proc.userdata["llm"] = llm.LLM.with_groq(
        model="llama-3.3-70b-versatile",
        temperature=0.8,          # FIXED: Enable creative storytelling
        max_tokens=4000,          # FIXED: Allow long stories (4000 tokens = ~3000 words)
        # Enable streaming for real-time response generation
    )   

    # proc.userdata["tts"] = rime.TTS(
    #         api_key=os.getenv("RIME_API"),
    #         model="mistv2",
    #         speaker="Tanya",
    #         reduce_latency=True,
    # )
    # proc.userdata["tts"] = elevenlabs.TTS()
    # # proc.userdata["tts"] = deepgram.TTS(
    # #     api_key=os.getenv("DEEPGRAM_API_KEY"),
    # #     model="aura-athena-en"
    # # )
    # Setup TTS with voice selection - can be extended to get from environment/agent config
    voice_id = os.getenv("VOICE_ID", "tara").lower()  # Get from environment or default to Tara
    
    # Get chunk size from environment variable (temporary solution until agent config is passed)
    chunk_size_bytes = int(os.getenv("CHUNK_SIZE_BYTES", "4096"))  # Default to balanced preset
    
    # FIXED: Create voice object - handle both streaming and non-streaming voice IDs
    from orpheus.tts import Voice
    if voice_id in ["tara", "tara_async"]:
        voice = Voice(id=voice_id, name="Tara")  # Use actual voice_id (with _async if specified)
    elif voice_id in ["elise", "elise_async"]:
        voice = Voice(id=voice_id, name="Elise")  # Use actual voice_id (with _async if specified)
    else:
        voice = Voice(id="tara", name="Tara")  # Default to Tara streaming
    
    # FIXED: Create improved word tokenizer to prevent character-by-character feeding
    # This will accumulate text better before passing to TTS
    improved_word_tokenizer = tokenize.basic.WordTokenizer(
        ignore_punctuation=False  # Keep punctuation for natural pauses
        # Note: split_character parameter removed for compatibility
    )
    
    print(f"🎤 Initializing TTS with voice: {voice.name} ({voice.id})")
    print(f"🎯 Using chunk size: {chunk_size_bytes} bytes")
    print(f"🔧 Using improved word tokenizer to prevent audio cutting")
    
    proc.userdata["tts"] = orpheus.OrpheusTTS(
        voice=voice, 
        chunk_size_bytes=chunk_size_bytes,
        word_tokenizer=improved_word_tokenizer  # Use improved tokenizer
    )
    # proc.userdata["tts"] = deepgram.TTS(
    #     api_key=os.getenv("DEEPGRAM_API_KEY"),
    #     model="aura-athena-en"
    # )
    # proc.userdata["tts"] =cartesia.TTS(
    #     model= "sonic-2", 
    #     # voice="bf0a246a-8642-498a-9950-80c35e9276b5"
    # )

if __name__ == "__main__":
    if not outbound_trunk_id or not outbound_trunk_id.startswith("ST_"):
        raise ValueError("SIP_OUTBOUND_TRUNK_ID is not set")
    opts = WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm, agent_name="DYNAMIC_WORKER_AGENT_NAME_PLACEHOLDER")
    cli.run_app(opts)
